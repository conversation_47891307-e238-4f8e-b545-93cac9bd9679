# Phorm Trading - Frontend

This is the frontend application for Phorm Trading, a funded prop firm platform.

## Tech Stack

- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- Axios for API calls

## Directory Structure

```
client/
├── app/            # Next.js App Router pages and layouts
├── components/     # Reusable UI components
├── lib/            # Utility functions and shared libraries
├── public/         # Static assets (images, fonts, etc.)
├── services/       # API services and external integrations
├── styles/         # Global styles (if needed beyond globals.css)
├── utils/          # Helper functions and utilities
```

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run the development server:
   ```bash
   npm run dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Building for Production

```bash
npm run build
```

## Environment Variables

Create a `.env.local` file in the client directory with the following variables:

```
# Backend API Configuration
NEXT_PUBLIC_API_BASE_URL=https://fxentra-ec0dfccfb73c.herokuapp.com

# Development API URL (fallback)
NEXT_PUBLIC_API_URL=http://localhost:5000

# Security Settings (automatically set based on NODE_ENV)
NODE_ENV=production
```