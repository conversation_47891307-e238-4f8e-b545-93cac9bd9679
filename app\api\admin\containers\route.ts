import { NextRequest, NextResponse } from 'next/server';

// Mock database for containers
let containers = [
  {
    id: 1,
    platform: "mt5",
    server: "FXentra-Live01",
    platform_login: "********",
    platform_password: "********",
    account_size: "100000",
    account_type: "phase1",
    is_assigned: true,
    order_id: ********,
    created_at: "2024-03-15T08:00:00.000Z",
    updated_at: "2024-03-15T08:00:00.000Z",
    status: "active"
  },
  {
    id: 2,
    platform: "mt5",
    server: "FXentra-Live02",
    platform_login: "********",
    platform_password: "********",
    account_size: "200000",
    account_type: "phase2",
    is_assigned: false,
    order_id: null,
    created_at: "2024-03-15T09:15:00.000Z",
    updated_at: "2024-03-15T09:15:00.000Z",
    status: "inactive"
  },
  {
    id: 3,
    platform: "mt5",
    server: "FXentra-Live03",
    platform_login: "********",
    platform_password: "********",
    account_size: "150000",
    account_type: "hft",
    is_assigned: true,
    order_id: ********,
    created_at: "2024-03-15T10:45:00.000Z",
    updated_at: "2024-03-15T10:45:00.000Z",
    status: "active"
  },
  {
    id: 4,
    platform: "mt5",
    server: "FXentra-Demo01",
    platform_login: "********",
    platform_password: "********",
    account_size: "25000",
    account_type: "phase1",
    is_assigned: false,
    order_id: null,
    created_at: "2024-03-15T11:30:00.000Z",
    updated_at: "2024-03-15T11:30:00.000Z",
    status: "pending"
  },
  {
    id: 5,
    platform: "mt5",
    server: "FXentra-Live04",
    platform_login: "********",
    platform_password: "********",
    account_size: "75000",
    account_type: "hft",
    is_assigned: true,
    order_id: ********,
    created_at: "2024-03-15T12:30:00.000Z",
    updated_at: "2024-03-15T12:30:00.000Z",
    status: "pending"
  },
  {
    id: 6,
    platform: "mt5",
    server: "FXentra-Demo02",
    platform_login: "********",
    platform_password: "********",
    account_size: "50000",
    account_type: "phase1",
    is_assigned: false,
    order_id: null,
    created_at: "2024-03-15T13:15:00.000Z",
    updated_at: "2024-03-15T13:15:00.000Z",
    status: "error"
  }
];

export async function GET() {
  try {
    return NextResponse.json(containers);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch containers' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate required fields
    const requiredFields = ['platform', 'server', 'platform_login', 'platform_password', 'account_size', 'account_type'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Create new container
    const newContainer = {
      ...data,
      id: containers.length + 1,
      is_assigned: false,
      order_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      status: 'inactive'
    };

    // Add to mock database
    containers.push(newContainer);

    return NextResponse.json(newContainer);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to create container' },
      { status: 500 }
    );
  }
}