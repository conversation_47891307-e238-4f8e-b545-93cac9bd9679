import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://fxentra-ec0dfccfb73c.herokuapp.com';

export async function GET(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'GET');
}

export async function POST(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'POST');
}

export async function PUT(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'PUT');
}

export async function DELETE(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'DELETE');
}

async function handleRequest(request: NextRequest, params: { path: string[] }, method: string) {
  try {
    // Construct the full backend URL
    const path = params.path.join('/');
    const fullUrl = `${API_BASE_URL}/auth/${path}`;
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams.toString();
    const apiUrl = searchParams ? `${fullUrl}?${searchParams}` : fullUrl;

    // Prepare headers
    const headers: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      if (!key.startsWith('next-') && key !== 'host') {
        headers[key] = value;
      }
    });

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };

    // Add body for POST/PUT requests
    if (method === 'POST' || method === 'PUT') {
      const contentType = request.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        requestOptions.body = JSON.stringify(await request.json());
      } else if (contentType?.includes('multipart/form-data')) {
        requestOptions.body = await request.formData();
        delete headers['content-type']; // Let fetch set the boundary
      } else {
        requestOptions.body = await request.text();
      }
    }

    console.log('Making request to:', apiUrl);

    // Make the actual API request
    const response = await fetch(apiUrl, requestOptions);
    
    // Get response data
    const responseData = await response.text();
    
    // Create response with original status and headers
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    });

    // Copy response headers
    response.headers.forEach((value, key) => {
      if (key !== 'content-encoding' && key !== 'content-length') {
        nextResponse.headers.set(key, value);
      }
    });

    return nextResponse;
  } catch (error) {
    console.error('API request error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 