import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';

type AccountTypeKey = 'instant' | 'oneStep' | 'twoStep';
type AccountSizeKey = 1000 | 3000 | 5000 | 10000 | 25000 | 50000 | 100000 | 200000;

interface AccountTypeInfo {
  name: string;
  description: string;
  phases: number;
  phasesLabel: string;
  targetMultiplier: number;
  maxCapital: number;
}

interface NewPricingSectionProps {
  isInDashboard?: boolean;
}

// Account types
const accountTypes: Record<AccountTypeKey, AccountTypeInfo> = {
  instant: {
    name: 'Instant',
    description: 'Start trading immediately with a funded account after completion of purchase.',
    phases: 0,
    phasesLabel: 'No evaluation',
    targetMultiplier: 10,
    maxCapital: 50000
  },
  oneStep: {
    name: '1-Step',
    description: 'Complete one evaluation to prove your trading skills before receiving a funded account.',
    phases: 1,
    phasesLabel: 'One-phase evaluation',
    targetMultiplier: 8,
    maxCapital: 100000
  },
  twoStep: {
    name: '2-Step',
    description: 'Complete a two-phase evaluation process before receiving a funded account.',
    phases: 2,
    phasesLabel: 'Two-phase evaluation',
    targetMultiplier: 6,
    maxCapital: 200000
  }
};

// Account sizes
const accountSizes = [
  { id: 1, value: 1000, label: "1k", discountPercent: 20 },
  { id: 2, value: 3000, label: "3k", discountPercent: 20 },
  { id: 3, value: 5000, label: "5k", discountPercent: 20 },
  { id: 4, value: 10000, label: "10k", discountPercent: 20 },
  { id: 5, value: 25000, label: "25k", discountPercent: 20 },
  { id: 6, value: 50000, label: "50k", discountPercent: 60 },
  { id: 7, value: 100000, label: "100k", discountPercent: 60 },
  { id: 8, value: 200000, label: "200k", discountPercent: 60 },
];

const NewPricingSection: React.FC<NewPricingSectionProps> = ({ isInDashboard = false }) => {
  const [accountType, setAccountType] = useState<AccountTypeKey>('instant');
  const [accountSize, setAccountSize] = useState<number>(5000);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    // Component initialization
  }, [isInDashboard]);

  // Update account size when switching account types to ensure valid selection
  useEffect(() => {
    if (accountType === 'instant' && accountSize > accountTypes[accountType].maxCapital) {
      setAccountSize(accountTypes[accountType].maxCapital);
    }
  }, [accountType, accountSize]);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  const getButtonHref = () => {
    const baseUrl = isInDashboard ? '/dashboard/place-order' : '/signup';
    const queryParams = `type=${accountType}&size=${accountSize}`;
    const href = `${baseUrl}?${queryParams}`;
    return href;
  };

  return (
    <section
      id="pricing"
      ref={sectionRef}
      className={`py-24 relative bg-[#030609] ${isVisible ? 'animate-fadeIn' : 'opacity-0'}`}
    >
      {/* Enhanced background elements for consistency */}
      <div className="absolute inset-0 z-0">
        {/* Refined gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        {/* Enhanced geometric elements */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#4FD1C5" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#2D3748" stopOpacity="0.4" />
              </linearGradient>
              <pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse">
                <path d="M 8 0 L 0 0 0 8" fill="none" stroke="url(#grid-gradient)" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect x="0" y="0" width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        {/* Enhanced glowing orbs */}
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-6xl font-bold text-white mb-6 md:mb-8">
            <span className="bg-gradient-to-r from-teal-400 via-emerald-300 to-teal-500 text-transparent bg-clip-text">FXentra</span> Trading Challenges
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-teal-500 to-emerald-400 mx-auto mb-6 md:mb-8"></div>
          <p className="text-gray-300 max-w-2xl mx-auto text-base md:text-lg leading-relaxed px-4">
            Select your FXentra account type and size that aligns with your trading strategy and goals.
            <span className="block text-teal-400 mt-2 font-medium">One-time payment, exclusive FXentra benefits.</span>
          </p>
        </div>

        {/* Account Type Tabs */}
        <div className="flex justify-center bg-dark/60 p-3 md:p-6">
          <div className="bg-dark/70 rounded-xl p-1 md:p-1.5 flex flex-col md:flex-row border border-gray-700/50 shadow-lg w-full md:w-auto">
            {Object.entries(accountTypes).map(([key, type]) => (
              <button
                key={key}
                onClick={() => setAccountType(key as AccountTypeKey)}
                className={`px-4 md:px-8 py-3 md:py-4 rounded-lg transition-all duration-300 font-medium text-sm md:text-base ${
                  accountType === key
                    ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20'
                    : 'text-gray-400 hover:text-white hover:bg-dark-light'
                }`}
              >
                {type.name}
              </button>
            ))}
          </div>
        </div>

        {/* Account Description */}
        <div className="text-center mt-4 md:mt-6 mb-4 md:mb-6 px-4">
          <p className="text-gray-300 max-w-2xl mx-auto text-base md:text-lg">
            {accountTypes[accountType].description}
          </p>
          <p className="text-gray-400 mt-2 md:mt-3 text-sm md:text-base">
            <span className="text-teal-400 font-medium">{accountTypes[accountType].phasesLabel}</span>
            {accountTypes[accountType].phases > 0 ? ` • ${accountTypes[accountType].phases} evaluation challenge${accountTypes[accountType].phases > 1 ? 's' : ''}` : ''}
          </p>
        </div>

        {/* Account Size Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-8 gap-3 p-4">
          {accountSizes
            .filter(size => size.value <= accountTypes[accountType].maxCapital)
            .map((size) => (
              <div
                key={size.id}
                onClick={() => setAccountSize(size.value)}
                className={`relative cursor-pointer p-4 text-center rounded-xl transition-all duration-300 border ${
                  accountSize === size.value
                    ? 'bg-gradient-to-b from-teal-600/80 to-teal-700/80 text-white border-teal-400/30 shadow-lg shadow-teal-500/20 transform scale-105'
                    : 'bg-dark/80 hover:bg-dark-light/80 text-gray-300 border-gray-800/50 hover:border-gray-700/50'
                }`}
              >
                {size.discountPercent > 0 && (
                  <div className="absolute -top-2 -right-2 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-[8px] md:text-[10px] font-bold py-0.5 px-1.5 rounded-full transform rotate-3 shadow-lg">
                    {size.discountPercent}% OFF
                  </div>
                )}
                <div className="font-bold text-base md:text-lg">{size.label}</div>
              </div>
            ))}
        </div>

        {/* CTA Button */}
        <div className="mt-8 text-center">
          <Link
            href={getButtonHref()}
            className="inline-block py-4 px-8 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold rounded-xl shadow-lg shadow-teal-500/30 hover:shadow-teal-500/50 transition-all duration-300 transform hover:translate-y-[-2px] text-center border border-teal-400/20"
          >
            {isInDashboard ? 'Place Order' : 'Start Now'}
          </Link>
        </div>

        {/* Payment Methods */}
        <div className="mt-8 text-center">
          <div className="text-gray-400 text-sm uppercase tracking-wide mb-3">We Accept</div>
          <div className="flex justify-center items-center gap-4">
            {/* Crypto Icons */}
            <div className="flex space-x-4">
              {['USDT', 'BNB', 'BTC', 'ETH'].map((crypto) => (
                <div key={crypto} className="flex flex-col items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    crypto === 'USDT' ? 'text-teal-400' :
                    crypto === 'BNB' ? 'text-yellow-400' :
                    crypto === 'BTC' ? 'text-orange-400' :
                    'text-blue-400'
                  }`}>
                    <span className="text-lg">₿</span>
                  </div>
                  <span className="text-xs mt-1 font-medium">{crypto}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Platform Support: MT4 & MT5 */}
        <div className="mt-6 text-center">
          <div className="flex justify-center items-center gap-6 mb-2">
            {/* MT4 Logo */}
            <span title="MetaTrader 4" className="inline-flex items-center">
              <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 md:h-10 md:w-10">
                <circle cx="19" cy="19" r="19" fill="#F6A800"/>
                <path d="M19 8c-6.075 0-11 4.925-11 11 0 6.075 4.925 11 11 11s11-4.925 11-11c0-6.075-4.925-11-11-11zm0 20c-4.971 0-9-4.029-9-9s4.029-9 9-9 9 4.029 9 9-4.029 9-9 9z" fill="#fff"/>
                <text x="50%" y="60%" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold" fontFamily="Arial">MT4</text>
              </svg>
            </span>
            {/* MT5 Logo */}
            <span title="MetaTrader 5" className="inline-flex items-center">
              <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 md:h-10 md:w-10">
                <circle cx="19" cy="19" r="19" fill="#4FD1C5"/>
                <path d="M19 8c-6.075 0-11 4.925-11 11 0 6.075 4.925 11 11 11s11-4.925 11-11c0-6.075-4.925-11-11-11zm0 20c-4.971 0-9-4.029-9-9s4.029-9 9-9 9 4.029 9 9-4.029 9-9 9z" fill="#fff"/>
                <text x="50%" y="60%" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold" fontFamily="Arial">MT5</text>
              </svg>
            </span>
          </div>
          <div className="text-gray-300 text-sm md:text-base font-medium">
            FXentra supports both <span className="text-[#F6A800] font-semibold">MetaTrader 4 (MT4)</span> and <span className="text-teal-400 font-semibold">MetaTrader 5 (MT5)</span> platforms for all challenges.
          </div>
        </div>
      </div>
    </section>
  );
};

export type { NewPricingSectionProps };
export default NewPricingSection;