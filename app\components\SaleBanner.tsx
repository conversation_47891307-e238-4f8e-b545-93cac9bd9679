'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

const offers = [
  {
    text: "🎯 Special Offer: 60% OFF on accounts up to $25k",
    link: "/#pricing"
  },
  {
    text: "🤝 Referral Program: Earn rewards for every successful referral",
    link: "/referral"
  },
  {
    text: "💎 Premium Trading: Start your funded journey today",
    link: "/signup"
  },
  {
    text: "🌟 Refer & Earn: Join our growing community of traders",
    link: "/referral"
  }
];

export default function SaleBanner() {
  const [currentOfferIndex, setCurrentOfferIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentOfferIndex((prevIndex) => (prevIndex + 1) % offers.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="relative h-[44px] bg-gradient-to-r from-[#1a1a1a] via-[#2a2a2a] to-[#1a1a1a] overflow-hidden border-b border-[#333]/20">
      {/* Luxury gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-emerald-500/10 to-teal-500/10"></div>
      
      {/* Animated light beam */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-shimmer"></div>
      
      {/* Content container */}
      <div className="absolute inset-0">
        <div className="relative h-full flex items-center overflow-hidden">
          <div className="animate-marquee whitespace-nowrap flex items-center h-full">
            {[...offers, ...offers].map((offer, index) => (
              <div
                key={index}
                className="inline-flex items-center justify-center h-[44px] px-12 text-sm font-medium"
                style={{
                  opacity: Math.floor(index % offers.length) === currentOfferIndex ? 1 : 0.3,
                  transition: 'opacity 0.5s ease-in-out'
                }}
              >
                <span className="bg-gradient-to-r from-white via-white/90 to-white bg-clip-text text-transparent">
                  {offer.text}{' '}
                </span>
                <Link 
                  href={offer.link} 
                  className="ml-3 px-3 py-1 rounded-full bg-gradient-to-r from-teal-500 to-emerald-500 text-xs uppercase tracking-wider hover:from-teal-400 hover:to-emerald-400 transition-all duration-300 font-bold"
                >
                  Learn More
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Side fade effects */}
      <div className="absolute inset-y-0 left-0 w-24 bg-gradient-to-r from-[#1a1a1a] to-transparent z-10"></div>
      <div className="absolute inset-y-0 right-0 w-24 bg-gradient-to-l from-[#1a1a1a] to-transparent z-10"></div>
      
      {/* Top highlight line */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
    </div>
  );
} 