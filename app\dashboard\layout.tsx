'use client';

import { useState, useEffect } from 'react';
import DashboardSidebar from '@/components/DashboardSidebar';
import DashboardNavbar from '@/components/DashboardNavbar';
import { Inter } from 'next/font/google';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Update CSS variable for sidebar width
  useEffect(() => {
    document.documentElement.style.setProperty(
      '--sidebar-width',
      isCollapsed ? '5rem' : '16rem'
    );
  }, [isCollapsed]);

  return (
    <div className="min-h-screen w-full bg-[#0B1221] flex">
      {/* Background decorative elements */}
      <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-dark to-dark opacity-90"></div>

        {/* Grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '30px 30px'
          }}
        ></div>

        {/* Geometric shapes */}
        <div className="geometric-shape shape-teal-glow w-[800px] h-[800px] -top-[400px] -left-[400px]"></div>
        <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] -bottom-[200px] -right-[200px]"></div>
        <div className="geometric-shape shape-teal-glow w-[500px] h-[500px] top-[30%] -right-[250px] opacity-[0.07]"></div>
      </div>

      {/* Navbar */}
 
      {/* Sidebar */}
      <DashboardSidebar onCollapse={(collapsed) => setIsCollapsed(collapsed)} />

      {/* Main content area with margin that matches sidebar width */}
      <div
        className="flex-1 min-h-screen overflow-y-auto transition-all duration-300 pt-20"
        style={{ marginLeft: 'var(--sidebar-width, 16rem)' }}
      >
        {children}
      </div>
    </div>
  );
}