'use client';

import React from 'react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/api';
import { userService, OrderDetails } from '@/services/userService';
import NewPricingSection from '@/components/NewPricingSection';

export default function DashboardPage() {
  const router = useRouter();
  const [orderIds, setOrderIds] = useState<string[]>([]);
  const [selectedOrderId, setSelectedOrderId] = useState<string>('');
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [noOrdersFound, setNoOrdersFound] = useState(false);

  // Check if user is authenticated
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user is authenticated and token is valid
        if (!authService.isAuthenticated()) {
          // Clear any stale data and logout
          authService.logout();
          setOrderIds([]);
          setSelectedOrderId('');
          setOrderDetails(null);
          setNoOrdersFound(false);

          // Redirect to login with return path and message if token was present but invalid
          const message = authService.getToken() ? 'Your session has expired. Please login again.' : '';
          router.replace(`/login?redirect=${encodeURIComponent('/dashboard')}${message ? `&message=${encodeURIComponent(message)}` : ''}`);
          return;
        }

        // Proceed with fetching order IDs only if authenticated
        try {
          const ids = await userService.getUserOrderIds();

          setOrderIds(ids);
          setNoOrdersFound(ids.length === 0);

          // Set the first order ID as selected or use the one from local storage
          const savedOrderId = userService.getSelectedOrderId();
          if (savedOrderId && ids.includes(savedOrderId)) {
            setSelectedOrderId(savedOrderId);
          } else if (ids.length > 0) {
            setSelectedOrderId(ids[0]);
            userService.saveSelectedOrderId(ids[0]);
          }
        } catch (error: any) {
          // If error is 401, token is invalid or expired
          if (error.response?.status === 401) {
            authService.logout();
            router.replace(`/login?redirect=${encodeURIComponent('/dashboard')}&message=${encodeURIComponent('Your session has expired. Please login again.')}`);
            return;
          }

          setNoOrdersFound(true);
        } finally {
          setIsLoading(false);
        }
      } catch (error) {
        authService.logout();
        router.replace(`/login?redirect=${encodeURIComponent('/dashboard')}`);
      }
    };

    checkAuth();
  }, [router]);

  // Fetch order details when selected order ID changes
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!selectedOrderId) return;

      setIsLoading(true);
      try {
        // Extract numeric part of order ID (remove "FxE" prefix)
        const numericOrderId = selectedOrderId.replace(/^FxE/, '');
        const details = await userService.getOrderDetails(numericOrderId);
        setOrderDetails(details);
      } catch (error) {
        // Error handling without logging
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderDetails();
  }, [selectedOrderId]);

  // Handle order selection change
  const handleOrderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const orderId = e.target.value;
    setSelectedOrderId(orderId);
    // Save the full order ID with prefix in local storage
    userService.saveSelectedOrderId(orderId);
  };

  // Check if order is incomplete or pending
  const isIncompleteOrPending = orderDetails?.status?.toLowerCase() === 'incomplete' ||
                               orderDetails?.status?.toLowerCase() === 'pending';

  // Use order details if available, otherwise use mock data
  const accountDetails = orderDetails ? {
    platform: orderDetails.platform || 'MT5',
    server: isIncompleteOrPending ? 'Not provided' : (orderDetails.server || 'FXentra-Live'),
    accountSize: orderDetails.account_size || '$25,000',
    profitTarget: orderDetails.profit_target ? `$${orderDetails.profit_target}` : (isIncompleteOrPending ? 'Not provided' : '$2,000'),
    accountType: orderDetails.challenge_type || 'Elite Trader',
    status: orderDetails.status || 'Active',
    loginId: isIncompleteOrPending ? 'Not provided' : (orderDetails.platform_login || 'MT5-8675309'),
    password: isIncompleteOrPending ? 'Not provided' : (showPassword ? orderDetails.platform_password : '••••••••'),
    dailyDrawdown: orderDetails.account_size ? `$${parseInt(orderDetails.account_size) * 0.05}` : '$1,250',
    overallDrawdown: orderDetails.account_size ? `$${parseInt(orderDetails.account_size) * 0.1}` : '$2,500',
  } : {
    platform: 'MT5',
    server: 'FXentra-Live',
    accountSize: '$25,000',
    profitTarget: '$2,000',
    accountType: 'Elite Trader',
    status: 'Active',
    loginId: 'MT5-8675309',
    password: '••••••••',
    dailyDrawdown: '$1,250',
    overallDrawdown: '$2,500',
  };

  // Animations for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
  };

  // Calculate drawdown percentages based on account type
  const getDrawdownLimits = (accountType: string) => {
    const type = accountType.toLowerCase();
    if (type.includes('onestep')) {
      return { daily: 4, overall: 8 };
    } else if (type.includes('twostep')) {
      return { daily: 4, overall: 10 };
    } else if (type.includes('instant')) {
      return { daily: 2.5, overall: 5 };
    }
    // Default values
    return { daily: 5, overall: 10 };
  };

  const drawdownLimits = getDrawdownLimits(accountDetails.accountType);

  return (
    <>
      {noOrdersFound ? (
        <div data-testid="pricing-section">
          <NewPricingSection />
        </div>
      ) : (
        <div className="w-full min-h-screen h-screen flex flex-col bg-gradient-to-b from-[#0A0F1B] to-[#0A1018]">
          {/* Premium Background Elements */}
          <div className="absolute inset-0 z-0 pointer-events-none overflow-hidden">
            {/* Grid pattern with lower opacity */}
            <div
              className="absolute inset-0 opacity-[0.03]"
              style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                backgroundSize: '30px 30px'
              }}
            />

            {/* Ambient glow effects */}
            <div className="absolute top-[5%] left-1/4 w-[900px] h-[900px] bg-teal-500/3 rounded-full blur-[300px] animate-pulse-slow mix-blend-screen"/>
            <div className="absolute bottom-[10%] right-1/4 w-[700px] h-[700px] bg-blue-500/3 rounded-full blur-[250px] animate-pulse-slow-delay mix-blend-screen"/>
            <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-black/20 to-transparent opacity-30" />
          </div>

          {/* Main Content - Full Height */}
          <div className="relative z-10 flex-grow flex flex-col w-full max-w-[1440px] mx-auto px-6 py-6">
            {/* Account Header Area */}
            <div className="flex justify-between items-center mb-7">
              <div className="relative group">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-teal-500/20 to-blue-500/20 rounded-lg blur opacity-0 group-hover:opacity-100 transition duration-500"></div>
                <select
                  value={selectedOrderId}
                  onChange={handleOrderChange}
                  className="relative appearance-none bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-lg px-5 py-2.5 text-white text-sm
                    focus:outline-none focus:ring-1 focus:ring-teal-500/40 focus:border-transparent
                    transition-all duration-300 hover:border-teal-500/40 pr-10 group-hover:shadow-[0_0_12px_rgba(20,184,166,0.15)]"
                >
                  {orderIds.length > 0 ? (
                    orderIds.map((orderId) => (
                      <option key={orderId} value={orderId}>
                        {orderId}
                      </option>
                    ))
                  ) : (
                    <option value="">No orders found</option>
                  )}
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none text-teal-500 group-hover:text-teal-400 transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-2 bg-gradient-to-r from-[#0F1A2E]/90 to-[#111A2F]/80 backdrop-blur-xl px-4 py-2 rounded-lg border border-teal-500/20
                  hover:border-teal-500/40 transition-all duration-300 hover:shadow-[0_0_12px_rgba(20,184,166,0.15)]"
              >
                <div className={`h-1.5 w-1.5 rounded-full animate-pulse ${
                  orderDetails?.status?.toLowerCase().includes('complet') || orderDetails?.status?.toLowerCase().includes('success')
                    ? 'bg-green-500'
                    : orderDetails?.status?.toLowerCase().includes('fail')
                    ? 'bg-red-500'
                    : orderDetails?.status?.toLowerCase().includes('live')
                    ? 'bg-purple-500'
                    : orderDetails?.status?.toLowerCase().includes('run')
                    ? 'bg-yellow-500'
                    : 'bg-teal-500'
                }`}></div>
                <span className={`font-medium text-sm ${
                  orderDetails?.status?.toLowerCase().includes('complet') || orderDetails?.status?.toLowerCase().includes('success')
                    ? 'text-green-500'
                    : orderDetails?.status?.toLowerCase().includes('fail')
                    ? 'text-red-500'
                    : orderDetails?.status?.toLowerCase().includes('live')
                    ? 'text-purple-500'
                    : orderDetails?.status?.toLowerCase().includes('run')
                    ? 'text-yellow-500'
                    : 'text-teal-500'
                }`}>
                  {orderDetails?.status || 'Active'}
                </span>
              </motion.div>
            </div>

            {/* Account Details Container - Flex Grow to Fill Height */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="flex-grow flex flex-col bg-gradient-to-br from-[#0C1424]/95 to-[#0F172A]/95 backdrop-blur-xl rounded-xl overflow-hidden
                shadow-[0_8px_30px_rgba(0,0,0,0.4)] border border-[#1E293B]/40 transition-all duration-500"
            >
              {/* Card Header */}
              <motion.div variants={itemVariants} className="relative border-b border-[#1E2A4A]/30">
                <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
                  <div className="absolute top-0 right-0 w-[600px] h-24 bg-gradient-to-l from-teal-500/5 via-blue-500/5 to-transparent transform rotate-12 translate-x-20 translate-y-2 opacity-80"></div>
                  <div className="absolute top-0 left-0 w-1/3 h-0.5 bg-gradient-to-r from-teal-500/40 to-transparent"></div>
                </div>
                <div className="px-7 py-6 relative">
                  <h2 className="text-2xl font-semibold mb-1 bg-gradient-to-r from-white via-teal-100 to-gray-200 bg-clip-text text-transparent flex items-center gap-2">
                    Account Details
                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-teal-400 to-teal-500 shadow-[0_0_5px_rgba(20,184,166,0.5)]"></div>
                  </h2>
                  <p className="text-gray-400 text-sm font-light">Manage your trading account credentials</p>
                </div>
              </motion.div>

              {/* Account Info Content - Flex Grow to Fill */}
              <div className="flex-grow flex flex-col p-7 relative overflow-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex flex-col items-center">
                      <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
                      <p className="mt-4 text-teal-500">Loading account details...</p>
                    </div>
                  </div>
                ) : (
                <>
                  {/* Account Info Cards - Premium Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
                  {[
                    {
                      icon: <svg className="w-5 h-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>,
                      label: "Platform",
                      value: accountDetails.platform,
                      color: "from-emerald-500/10 to-teal-500/5"
                    },
                    {
                      icon: <svg className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                      </svg>,
                      label: "Server",
                      value: accountDetails.server,
                      color: "from-blue-500/10 to-indigo-500/5"
                    },
                    {
                      icon: <svg className="w-5 h-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>,
                      label: "Account Size",
                      value: accountDetails.accountSize,
                      highlight: true,
                      border: "border-amber-500/20",
                      color: "from-amber-600/20 to-amber-500/5"
                    },
                    {
                      icon: <svg className="w-5 h-5 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>,
                      label: "Profit Target",
                      value: accountDetails.profitTarget,
                      color: "from-teal-500/10 to-emerald-500/5"
                    },
                    {
                      icon: <svg className="w-5 h-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>,
                      label: "Account Type",
                      value: accountDetails.accountType,
                      color: "from-purple-500/10 to-indigo-500/5"
                    },
                    {
                      icon: <svg className="w-5 h-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>,
                      label: "Status",
                      value: (() => {
                        const status = accountDetails.status?.toLowerCase() || '';
                        if (status.includes('incomplet')) return 'Incomplete';
                        if (status.includes('complet')) return 'Completed';
                        if (status.includes('fail')) return 'Failed';
                        if (status.includes('stage') || status.includes('stage_2')) return 'Stage 2';
                        if (status.includes('live')) return 'Live';
                        if (status.includes('run')) return 'Running';
                        if (status.includes('active')) return 'Active';
                        if (status.includes('inactive')) return 'Inactive';
                        if (status.includes('pass')) return 'Passed';
                        return accountDetails.status;
                      })(),
                      color: (() => {
                        const status = accountDetails.status?.toLowerCase() || '';
                        if (status.includes('incomplet')) return 'from-gray-500/10 to-gray-500/5';
                        if (status.includes('complet') || status.includes('pass')) return 'from-green-500/10 to-green-500/5';
                        if (status.includes('fail')) return 'from-red-500/10 to-red-500/5';
                        if (status.includes('stage') || status.includes('stage_2')) return 'from-blue-500/10 to-blue-500/5';
                        if (status.includes('live')) return 'from-purple-500/10 to-purple-500/5';
                        if (status.includes('run') || status.includes('active')) return 'from-yellow-500/10 to-yellow-500/5';
                        if (status.includes('inactive')) return 'from-gray-500/10 to-gray-500/5';
                        return 'from-green-500/10 to-green-500/5';
                      })(),
                      border: (() => {
                        const status = accountDetails.status?.toLowerCase() || '';
                        if (status.includes('incomplet')) return 'border-gray-500/20';
                        if (status.includes('complet') || status.includes('pass')) return 'border-green-500/20';
                        if (status.includes('fail')) return 'border-red-500/20';
                        if (status.includes('stage') || status.includes('stage_2')) return 'border-blue-500/20';
                        if (status.includes('live')) return 'border-purple-500/20';
                        if (status.includes('run') || status.includes('active')) return 'border-yellow-500/20';
                        if (status.includes('inactive')) return 'border-gray-500/20';
                        return 'border-green-500/20';
                      })()
                    },
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      variants={itemVariants}
                      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
                      className="group relative"
                    >
                      <div className={`relative bg-gradient-to-br ${item.color} backdrop-blur-xl p-4 rounded-lg ${item.border || 'border border-[#1E293B]/50'}
                        hover:border-[#1E293B]/80 transition-all duration-300 h-full flex flex-col justify-between overflow-hidden`}>
                        {/* Subtle Background Decoration */}
                        <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-white/5 to-transparent rounded-full blur-xl opacity-20 transform translate-x-8 -translate-y-8"></div>

                        <div className="flex items-center gap-3 mb-3">
                          <div className="bg-[#0A1120] p-2 rounded-md">{item.icon}</div>
                          <div className="text-gray-400 text-sm font-light">{item.label}</div>
                        </div>

                        <div className="text-white font-medium text-base group-hover:text-teal-300 transition-colors duration-300 pl-1">
                          {item.value}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                  </div>

                {/* Main Content Area - Enhanced Layout - Flex Grow */}
                <div className="flex-grow grid grid-cols-1 md:grid-cols-12 gap-6 h-full">
                  {/* Left Column - Drawdown Limits */}
                  <motion.div variants={itemVariants} className="md:col-span-5 flex flex-col">
                    <div className="flex items-center gap-2 mb-3.5">
                      <div className="h-5 w-0.5 bg-gradient-to-b from-red-400 to-transparent rounded-full"></div>
                      <div className="flex items-center gap-1.5">
                        <svg className="w-4 h-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                        <h3 className="text-base font-medium text-gray-200">Drawdown Limits</h3>
                      </div>
                    </div>

                    {/* Drawdown Cards - Luxury Style */}
                    <div className="space-y-4 flex-grow">
                      {[
                        {
                          title: "Daily Drawdown Limit",
                          percent: `${drawdownLimits.daily}%`,
                          subtitle: "of account size",
                          icon: <svg className="w-4 h-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>,
                          dashArray: `${drawdownLimits.daily}, 100`
                        },
                        {
                          title: "Overall Drawdown Limit",
                          percent: `${drawdownLimits.overall}%`,
                          subtitle: "of account size",
                          icon: <svg className="w-4 h-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>,
                          dashArray: `${drawdownLimits.overall}, 100`
                        }
                      ].map((item, index) => (
                        <motion.div
                          key={index}
                          variants={itemVariants}
                          whileHover={{ scale: 1.01, transition: { duration: 0.2 } }}
                          className="group relative"
                        >
                          <div className="p-5 bg-gradient-to-br from-[#1A1726]/90 to-[#191431]/90 rounded-xl border border-red-500/10 hover:border-red-500/20 transition-all duration-300 group-hover:shadow-[0_4px_15px_rgba(185,28,28,0.08)]">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3.5">
                                <div className="bg-[#0F1223] p-2.5 rounded-lg flex-shrink-0 shadow-inner border border-red-500/5">
                                  {item.icon}
                                </div>
                                <div className="flex flex-col">
                                  <div className="text-base font-medium text-gray-300 group-hover:text-gray-200 transition-colors duration-300">
                                    {item.title}
                                  </div>
                                  <div className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-0.5">
                                    {item.subtitle}
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center">
                                <div className="w-14 h-14 relative">
                                  <svg className="w-full h-full rotate-[-90deg]" viewBox="0 0 36 36">
                                    {/* Background Circle */}
                                    <circle cx="18" cy="18" r="15.9155" fill="none" stroke="#1A1726" strokeWidth="2.5" />
                                    {/* Progress Circle */}
                                    <circle
                                      cx="18"
                                      cy="18"
                                      r="15.9155"
                                      fill="none"
                                          stroke="url(#gradient)"
                                      strokeWidth="2.5"
                                      strokeDasharray={item.dashArray}
                                          strokeDashoffset="0"
                                        />
                                  </svg>
                                      <div className="absolute inset-0 flex items-center justify-center text-white text-sm font-medium">
                                    {item.percent}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>

                  {/* Right Column - Account Credentials */}
                  <motion.div variants={itemVariants} className="md:col-span-7 flex flex-col">
                    <div className="flex items-center gap-2 mb-3.5">
                      <div className="h-5 w-0.5 bg-gradient-to-b from-teal-400 to-transparent rounded-full"></div>
                      <div className="flex items-center gap-1.5">
                        <svg className="w-4 h-4 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                        <h3 className="text-base font-medium text-gray-200">Account Credentials</h3>
                      </div>
                    </div>

                    {/* Credentials Cards */}
                    <div className="space-y-4 flex-grow">
                      {[
                        {
                          label: "Login ID",
                          value: accountDetails.loginId,
                          icon: <svg className="w-4 h-4 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        },
                        {
                          label: "Password",
                          value: accountDetails.password,
                          icon: <svg className="w-4 h-4 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>,
                          isPassword: true
                        },
                        {
                          label: "Server",
                          value: accountDetails.server,
                          icon: <svg className="w-4 h-4 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                          </svg>
                        }
                      ].map((item, index) => (
                        <motion.div
                          key={index}
                          variants={itemVariants}
                          whileHover={{ scale: 1.01, transition: { duration: 0.2 } }}
                          className="group relative"
                        >
                          <div className="p-5 bg-gradient-to-br from-[#1A1726]/90 to-[#191431]/90 rounded-xl border border-teal-500/10 hover:border-teal-500/20 transition-all duration-300 group-hover:shadow-[0_4px_15px_rgba(20,184,166,0.08)]">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3.5">
                                <div className="bg-[#0F1223] p-2.5 rounded-lg flex-shrink-0 shadow-inner border border-teal-500/5">
                                  {item.icon}
                                </div>
                                <div className="text-base font-medium text-gray-300 group-hover:text-gray-200 transition-colors duration-300">
                                  {item.label}
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <input
                                  type={item.isPassword ? (showPassword ? "text" : "password") : "text"}
                                  value={item.value}
                                  readOnly
                                  className={`bg-[#0F1223]/50 border rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-1 focus:border-transparent ${
                                    item.value === 'Not provided'
                                      ? 'border-gray-700/20 text-gray-500 italic'
                                      : 'border-teal-500/10 text-gray-300 focus:ring-teal-500/20'
                                  }`}
                                />
                                <button
                                  onClick={() => {
                                    if (item.value !== 'Not provided') {
                                      navigator.clipboard.writeText(item.value);
                                    }
                                  }}
                                  disabled={item.value === 'Not provided'}
                                  className={`p-1.5 rounded-lg bg-[#0F1223] border ${
                                    item.value === 'Not provided'
                                      ? 'border-gray-700/20 cursor-not-allowed'
                                      : 'border-teal-500/10 hover:border-teal-500/20 transition-colors duration-300'
                                  }`}
                                >
                                  <svg
                                    className={`w-4 h-4 ${item.value === 'Not provided' ? 'text-gray-500' : 'text-teal-400'}`}
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                  </svg>
                                </button>
                                {item.isPassword && !isIncompleteOrPending && (
                                  <button
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="p-1.5 rounded-lg bg-[#0F1223] border border-teal-500/10 hover:border-teal-500/20 transition-colors duration-300"
                                  >
                                    <svg className="w-4 h-4 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>
                </>
                )}
              </div>
            </motion.div>
          </div>

          {/* Update any references to isIncomplete in the JSX */}
          {isIncompleteOrPending && (
            <div className="bg-yellow-500/10 border border-yellow-500/20 text-yellow-400 px-4 py-3 rounded-lg mb-6">
              Your order is being processed. Account credentials will be provided once the order is approved.
            </div>
          )}
        </div>
      )}
    </>
  );
}