'use client';

import { useState, useEffect, Suspense } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';

type AccountTypeKey = 'instant' | 'oneStep' | 'twoStep';
type AccountSizeKey = 1000 | 3000 | 5000 | 10000 | 25000 | 50000 | 100000 | 200000;

const pricingStructure: Record<AccountTypeKey, Record<AccountSizeKey, { real: number, discounted: number }>> = {
  instant: {
    1000: { real: 61.25, discounted: 49 },
    3000: { real: 143.75, discounted: 115 },
    5000: { real: 218.75, discounted: 175 },
    10000: { real: 416.25, discounted: 333 },
    25000: { real: 856.25, discounted: 685 },
    50000: { real: 2450, discounted: 735 },
    100000: { real: 4166.67, discounted: 1250 },
    200000: { real: 0, discounted: 0 }
  },
  oneStep: {
    1000: { real: 12.5, discounted: 10 },
    3000: { real: 16.25, discounted: 13 },
    5000: { real: 27.5, discounted: 22 },
    10000: { real: 43.75, discounted: 35 },
    25000: { real: 86.25, discounted: 69 },
    50000: { real: 200, discounted: 60 },
    100000: { real: 283.33, discounted: 85 },
    200000: { real: 500, discounted: 150 }
  },
  twoStep: {
    1000: { real: 10, discounted: 8 },
    3000: { real: 12.5, discounted: 10 },
    5000: { real: 21.25, discounted: 17 },
    10000: { real: 32.5, discounted: 26 },
    25000: { real: 72.5, discounted: 58 },
    50000: { real: 140, discounted: 42 },
    100000: { real: 216.67, discounted: 65 },
    200000: { real: 350, discounted: 105 }
  }
};

interface OrderFormState {
  challengeType: AccountTypeKey | '';
  accountSize: string;
  platform: string;
  paymentMethod: string;
  transactionId: string;
  paymentProof: File | null;
  email: string;
}

function PlaceOrderContent() {
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [orderForm, setOrderForm] = useState<OrderFormState>({
    challengeType: '',
    accountSize: '',
    platform: '',
    paymentMethod: 'USDT (BEP20) - No Fee',
    transactionId: '',
    paymentProof: null,
    email: ''
  });
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);

  useEffect(() => {
    // Get access token and email from localStorage
    const token = localStorage.getItem('token');
    const userEmail = localStorage.getItem('user_email');

    if (token) {
      setAccessToken(token);
    }

    if (userEmail) {
      setOrderForm(prev => ({ ...prev, email: userEmail }));
    }

    // Get type and size from URL parameters
    const type = searchParams.get('type');
    const size = searchParams.get('size');

    if (type && size) {
      setOrderForm(prev => ({
        ...prev,
        challengeType: type as AccountTypeKey,
        accountSize: size
      }));
    }
  }, [searchParams]);

  // Get available account sizes based on challenge type
  const getAvailableAccountSizes = (type: AccountTypeKey | '') => {
    if (!type) return [];

    return Object.entries(pricingStructure[type as AccountTypeKey])
      .filter(([_, price]) => price.real > 0) // Filter out unavailable sizes (price = 0)
      .map(([size]) => ({
        value: size,
        label: Number(size) >= 1000 ? `$${Number(size).toLocaleString('en-US')}` : `$${size}`
      }));
  };

  const steps = [
    { number: 1, title: 'Challenge Details' },
    { number: 2, title: 'Payment Information' },
    { number: 3, title: 'Review & Complete' }
  ];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setOrderForm(prev => {
      // Reset account size when changing challenge type
      if (name === 'challengeType') {
        return {
          ...prev,
          challengeType: value as AccountTypeKey | '',
          accountSize: ''
        };
      }
      return { ...prev, [name]: value };
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setOrderForm(prev => ({ ...prev, paymentProof: file }));

      // Create image preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const [orderSuccess, setOrderSuccess] = useState(false);
  const [orderError, setOrderError] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);

  const handleSubmit = async () => {
    try {
      // Reset error state
      setOrderError(null);

      // Validate required fields
      if (!accessToken) {
        setOrderError('You must be logged in to place an order');
        return;
      }

      if (!orderForm.email) {
        setOrderError('Email is required');
        return;
      }

      if (!orderForm.challengeType) {
        setOrderError('Challenge type is required');
        return;
      }

      if (!orderForm.accountSize) {
        setOrderError('Account size is required');
        return;
      }

      if (!orderForm.platform) {
        setOrderError('Trading platform is required');
        return;
      }

      if (!orderForm.transactionId) {
        setOrderError('Transaction ID is required');
        return;
      }

      if (!orderForm.paymentProof) {
        setOrderError('Payment proof image is required');
        return;
      }

      setIsSubmitting(true);

      // Create FormData
      const formData = new FormData();
      formData.append('email', orderForm.email);
      formData.append('challenge_type', orderForm.challengeType);
      formData.append('account_size', orderForm.accountSize);
      formData.append('platform', orderForm.platform);
      formData.append('payment_method', orderForm.paymentMethod);
      formData.append('txid', orderForm.transactionId);
      formData.append('image', orderForm.paymentProof);



      // Import the orderService
      const { placeOrder } = await import('@/services/orderService');

      // Submit order using the service
      const response = await placeOrder(formData);

      // Set success state and order ID
      setOrderSuccess(true);
      setOrderId(response.order_id);

      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 3000);

    } catch (error: any) {
      setOrderError(error.message || 'Failed to place order. Please try again.');

      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        setOrderError('Network error. Please check your internet connection and try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(prev => prev + 1);
    } else {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) setCurrentStep(prev => prev - 1);
  };

  // Get available account sizes for the current challenge type
  const availableAccountSizes = getAvailableAccountSizes(orderForm.challengeType as AccountTypeKey);

  // Payment method options
  const paymentMethods = [
    {
      value: 'USDT (BEP20) - No Fee',
      label: 'USDT (BEP20) - No Fee',
      logo: 'https://assets.coingecko.com/coins/images/325/large/Tether.png',
    },
    {
      value: 'USDT TRC20',
      label: 'USDT TRC20',
      logo: 'https://assets.coingecko.com/coins/images/325/large/Tether.png',
    },
    {
      value: 'USDT Polygon PoS/Matic',
      label: 'USDT Polygon PoS/Matic',
      logo: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
    },
    {
      value: 'BNB BEP20',
      label: 'BNB BEP20',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/5/57/Binance_Logo.png',
    },
    {
      value: 'ETH (ERC20)',
      label: 'ETH (ERC20)',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/0/05/Ethereum_logo_2014.svg',
    },
    {
      value: 'SOLANA',
      label: 'SOLANA',
      logo: 'https://upload.wikimedia.org/wikipedia/en/b/b9/Solana_logo.png',
    },
    {
      value: 'BTC',
      label: 'BTC',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/4/46/Bitcoin.svg',
    },
  ];

  // Payment addresses mapping
  const paymentAddresses = {
    'USDT (BEP20) - No Fee': '******************************************',
    'USDT TRC20': 'TNM1RisTogzSD3A2LBzPcKyjhh36RxJFt1',
    'USDT Polygon PoS/Matic': '******************************************',
    'BNB BEP20': '******************************************',
    'ETH (ERC20)': '******************************************',
    'SOLANA': 'AKnqNRTsiKv8aKxV4nBsu4QmSJbY8XWRtXsyRnwbk9sQ',
    'BTC': '******************************************'
  };

  // Price display logic
  const realPrice = orderForm.challengeType && orderForm.accountSize ? pricingStructure[orderForm.challengeType as AccountTypeKey][parseInt(orderForm.accountSize) as AccountSizeKey].real : 0;
  const discountedPrice = orderForm.challengeType && orderForm.accountSize ? pricingStructure[orderForm.challengeType as AccountTypeKey][parseInt(orderForm.accountSize) as AccountSizeKey].discounted : 0;
  const savings = realPrice - discountedPrice;

  return (
    <div className="h-screen bg-[#0B1221] overflow-y-auto">
      {/* Background elements */}
      <div className="fixed inset-0">
        <div className="absolute inset-0 opacity-[0.07]" style={{
          backgroundImage: `
            linear-gradient(to right, rgba(45, 212, 191, 0.2) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(45, 212, 191, 0.2) 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, rgba(45, 212, 191, 0.1) 0%, transparent 50%)
          `,
          backgroundSize: '40px 40px, 40px 40px, 100% 100%'
        }}></div>

        <div className="fixed top-0 left-1/4 w-[600px] h-[600px] bg-teal-400/5 rounded-full blur-[120px] animate-pulse-slow mix-blend-screen"></div>
        <div className="fixed bottom-0 right-1/4 w-[600px] h-[600px] bg-teal-400/5 rounded-full blur-[120px] animate-pulse-slow-delay mix-blend-screen"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container max-w-3xl mx-auto px-4 py-12">
        {/* Badge */}
        <div className="flex justify-center mb-6">
          <div className="bg-[#0F1A2E]/80 backdrop-blur-xl px-6 py-2 rounded-full border border-teal-400/20">
            <span className="text-teal-400 text-sm font-medium tracking-wide">SOVEREIGN WEALTH SERIES</span>
          </div>
        </div>

        <div className="flex flex-col min-h-[calc(100vh-6rem)]">
          {/* Content */}
          <div className="flex-grow flex flex-col">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center mb-10"
            >
              <h1 className="text-4xl font-bold text-white mb-3 tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-200">
                  Order Your Challenge
                </span>
              </h1>
              <p className="text-gray-400 text-lg font-light">Select your preferred challenge parameters below</p>
            </motion.div>

            {/* Progress Steps */}
            <div className="flex justify-between items-center mb-8 relative px-8">
              <div className="absolute left-0 right-0 h-[2px] bg-[#1E293B] top-1/2 transform -translate-y-1/2 z-0">
                <div
                  className="h-full bg-gradient-to-r from-teal-400 to-teal-300 transition-all duration-500 ease-out"
                  style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
                ></div>
              </div>

              {steps.map((step, index) => (
                <motion.div
                  key={step.number}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative z-10 flex flex-col items-center group"
                >
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center text-base font-medium mb-2 transition-all duration-300 ease-out
                      ${currentStep >= step.number
                        ? 'bg-teal-400 text-[#0B1221] shadow-lg shadow-teal-400/20'
                        : 'bg-[#1E293B] text-gray-400'}`}
                  >
                    <span className="relative z-10">{step.number}</span>
                    {currentStep >= step.number && (
                      <div className="absolute inset-0 rounded-full bg-teal-400 opacity-20 blur-sm"></div>
                    )}
                  </div>
                  <span className={`text-sm font-medium transition-all duration-300
                    ${currentStep >= step.number ? 'text-teal-400' : 'text-gray-500'}`}>
                    {step.title}
                  </span>
                </motion.div>
              ))}
            </div>

            {/* Form Container */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-[#0F1A2E]/90 backdrop-blur-2xl rounded-2xl border border-teal-400/20 p-8 flex-grow overflow-y-auto"
            >
              {/* Step 1 */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div className="group">
                    <label className="block text-sm font-medium text-gray-400 mb-2 group-hover:text-teal-400 transition-colors">
                      Challenge Type<span className="text-teal-400">*</span>
                    </label>
                    <div className="relative">
                      <select
                        name="challengeType"
                        value={orderForm.challengeType}
                        onChange={handleChange}
                        className="w-full bg-[#1E293B]/50 border border-teal-400/20 rounded-xl py-3.5 px-4 text-white focus:outline-none focus:ring-2 focus:ring-teal-400/40 focus:border-transparent transition-all duration-300 appearance-none hover:border-teal-400/40"
                      >
                        <option value="">Select Challenge</option>
                        <option value="instant">Instant</option>
                        <option value="oneStep">1-Step Challenge</option>
                        <option value="twoStep">2-Step Challenge</option>
                      </select>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className="group">
                    <label className="block text-sm font-medium text-gray-400 mb-2 group-hover:text-teal-400 transition-colors">
                      Account Size<span className="text-teal-400">*</span>
                    </label>
                    <div className="relative">
                      <select
                        name="accountSize"
                        value={orderForm.accountSize}
                        onChange={handleChange}
                        disabled={!orderForm.challengeType}
                        className="w-full bg-[#1E293B]/50 border border-teal-400/20 rounded-xl py-3.5 px-4 text-white focus:outline-none focus:ring-2 focus:ring-teal-400/40 focus:border-transparent transition-all duration-300 appearance-none hover:border-teal-400/40 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <option value="">Select Size</option>
                        {availableAccountSizes.map(size => (
                          <option key={size.value} value={size.value}>
                            {size.label}
                          </option>
                        ))}
                      </select>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className="group">
                    <label className="block text-sm font-medium text-gray-400 mb-2 group-hover:text-teal-400 transition-colors">
                      Trading Platform<span className="text-teal-400">*</span>
                    </label>
                    <div className="relative">
                      <select
                        name="platform"
                        value={orderForm.platform}
                        onChange={handleChange}
                        className="w-full bg-[#1E293B]/50 border border-teal-400/20 rounded-xl py-3.5 px-4 text-white focus:outline-none focus:ring-2 focus:ring-teal-400/40 focus:border-transparent transition-all duration-300 appearance-none hover:border-teal-400/40"
                      >
                        <option value="">Select Platform</option>
                        <option value="mt4">MetaTrader 4</option>
                        <option value="mt5">MetaTrader 5</option>
                      </select>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Price Display */}
                  {orderForm.accountSize && orderForm.challengeType && (
                    <div className="mt-3 bg-[#1A1E2E]/80 rounded-lg border border-teal-400/10 p-4 transition-all duration-300">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-400 text-sm">Real Price:</span>
                        <span className="text-gray-400 text-sm line-through">
                          ${realPrice}
                        </span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-gray-200 text-sm font-medium">Discounted Price:</span>
                        <span className="text-teal-400 font-medium">
                          ${discountedPrice}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400 text-xs">You Save:</span>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-200 text-xs">
                            ${savings}
                          </span>
                          <span className="bg-teal-500/20 text-teal-300 text-xs px-2 py-0.5 rounded">
                            {realPrice > 0 ? `${((savings / realPrice) * 100).toFixed(0)}% OFF` : ''}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Step 2 */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Email</label>
                    <input
                      type="email"
                      name="email"
                      value={orderForm.email}
                      readOnly
                      className="w-full bg-[#0B1221] border border-teal-400/20 rounded-xl py-3 px-4 text-white/50 focus:outline-none focus:ring-2 focus:ring-teal-400/40 focus:border-transparent transition-all hover:border-teal-400/40"
                      placeholder="Email will be auto-filled from your account"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Payment Method*</label>
                    <div className="flex flex-wrap gap-3">
                      {paymentMethods.map((method) => (
                        <button
                          type="button"
                          key={method.value}
                          onClick={() => setOrderForm((prev) => ({ ...prev, paymentMethod: method.value }))}
                          className={`flex flex-col items-center justify-center w-28 h-28 p-3 rounded-xl border transition-all duration-200 shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-400/40
                            ${orderForm.paymentMethod === method.value
                              ? 'border-teal-400 bg-teal-400/10 ring-2 ring-teal-400/30'
                              : 'border-teal-400/20 bg-[#0B1221] hover:border-teal-400/40'}`}
                          aria-label={method.label}
                        >
                          <img
                            src={method.logo}
                            alt={method.label + ' logo'}
                            className="w-10 h-10 mb-2 object-contain"
                            loading="lazy"
                          />
                          <span className="text-xs text-white font-medium text-center">
                            {method.label}
                          </span>
                          {orderForm.paymentMethod === method.value && (
                            <span className="mt-1 text-teal-400 text-xs font-bold">Selected</span>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="bg-gradient-to-b from-[#0F1A2E]/80 to-[#0B1221]/90 border border-teal-400/30 rounded-2xl p-6 shadow-lg shadow-teal-400/5">
                    <div className="text-center mb-4">
                      <div className="inline-block bg-gradient-to-r from-teal-400/20 to-blue-400/20 px-6 py-2 rounded-full backdrop-blur-sm mb-2">
                        <h3 className="text-white font-semibold text-lg">
                          Send Payment to the {orderForm.paymentMethod.split(' ')[0]} address
                        </h3>
                      </div>
                      <div className="h-px w-3/4 mx-auto bg-gradient-to-r from-transparent via-teal-400/30 to-transparent"></div>
                    </div>

                    <div className="flex flex-col md:flex-row items-center gap-6">
                      <div className="w-32 h-32 bg-white rounded-xl flex items-center justify-center flex-shrink-0 shadow-md shadow-teal-400/10 border border-teal-400/20 p-1.5">
                        {orderForm.paymentMethod === "USDT (BEP20) - No Fee" ? (
                          <Image
                            src="/images/Usdt bep20.jpg"
                            alt="USDT BEP20 QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : orderForm.paymentMethod === "USDT TRC20" ? (
                          <Image
                            src="/images/Usdt trc 20.jpg"
                            alt="USDT TRC20 QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : orderForm.paymentMethod === "USDT Polygon PoS/Matic" ? (
                          <Image
                            src="/images/Usdt polygon.jpg"
                            alt="USDT Polygon QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : orderForm.paymentMethod === "BNB BEP20" ? (
                          <Image
                            src="/images/BNB.jpg"
                            alt="BNB QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : orderForm.paymentMethod === "ETH (ERC20)" ? (
                          <Image
                            src="/images/ETH.jpg"
                            alt="ETH QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : orderForm.paymentMethod === "SOLANA" ? (
                          <Image
                            src="/images/SOLONA.jpg"
                            alt="SOLANA QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : orderForm.paymentMethod === "BTC" ? (
                          <Image
                            src="/images/BTC.jpg"
                            alt="BTC QR Code"
                            width={128}
                            height={128}
                            className="rounded-lg"
                          />
                        ) : (
                          <div className="w-full h-full bg-black rounded-lg flex items-center justify-center">
                            <div className="text-white/30 text-sm">QR Code</div>
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0 space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-teal-300 mb-2 ml-1">Payment Address</label>
                          <div className="flex gap-2 items-center bg-[#080F1A] border border-teal-400/30 rounded-xl p-1 pl-4 shadow-inner relative overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-teal-500/5 to-transparent opacity-70 blur-lg"></div>
                            <input
                              type="text"
                              value={paymentAddresses[orderForm.paymentMethod as keyof typeof paymentAddresses]}
                              readOnly
                              className="flex-1 min-w-0 bg-transparent border-none py-3 text-white text-sm overflow-hidden text-ellipsis focus:outline-none"
                            />
                            <button
                              onClick={() => navigator.clipboard.writeText(paymentAddresses[orderForm.paymentMethod as keyof typeof paymentAddresses])}
                              className="p-3 hover:bg-teal-400/10 rounded-lg transition-all flex-shrink-0 group"
                            >
                              <svg className="w-5 h-5 text-teal-400 group-hover:text-teal-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>

                        <div className="flex flex-col lg:flex-row gap-2">
                          <div className="bg-gradient-to-r from-blue-500/10 to-teal-500/10 border border-blue-400/20 rounded-lg px-4 py-3 shadow-sm">
                            <div className="flex items-center gap-2">
                              <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <p className="text-blue-100 text-sm font-medium">
                                For payments under $10, use <span className="text-teal-300 font-semibold">USDT Polygon PoS/Matic</span>
                              </p>
                            </div>
                          </div>

                          <div className="bg-teal-500/10 border border-teal-400/20 rounded-lg px-4 py-3 shadow-sm">
                            <div className="flex items-center gap-2">
                              <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                              <p className="text-teal-100 text-sm font-medium">
                                Secured, verified transactions
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 flex justify-center gap-6">
                      <div className="flex flex-col items-center">
                        <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mb-1">
                          <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15 16L12 13M12 13L9 10M12 13V8" />
                          </svg>
                        </div>
                        <span className="text-teal-300 text-xs font-medium">Secure</span>
                      </div>

                      <div className="flex flex-col items-center">
                        <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mb-1">
                          <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <span className="text-teal-300 text-xs font-medium">Fast</span>
                      </div>

                      <div className="flex flex-col items-center">
                        <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mb-1">
                          <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                        </div>
                        <span className="text-teal-300 text-xs font-medium">Verified</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Transaction TXID*</label>
                      <input
                        type="text"
                        name="transactionId"
                        value={orderForm.transactionId}
                        onChange={handleChange}
                        placeholder="Enter your transaction ID"
                        className="w-full bg-[#0B1221] border border-teal-400/20 rounded-xl py-3 px-4 text-white focus:outline-none focus:ring-2 focus:ring-teal-400/40 focus:border-transparent transition-all hover:border-teal-400/40"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Payment Proof*</label>
                      <div className="border border-dashed border-teal-400/30 rounded-xl p-6 text-center hover:border-teal-400/50 transition-all bg-gradient-to-b from-teal-400/5 to-transparent">
                        <input
                          type="file"
                          name="paymentProof"
                          onChange={handleFileChange}
                          className="hidden"
                          id="paymentProof"
                          accept="image/*"
                        />
                        <label htmlFor="paymentProof" className="cursor-pointer">
                          <div className="flex flex-col items-center gap-3">
                            {imagePreview ? (
                              <div className="relative w-32 h-32 rounded-lg overflow-hidden">
                                <img
                                  src={imagePreview}
                                  alt="Payment proof preview"
                                  className="w-full h-full object-cover"
                                />
                                <button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setImagePreview(null);
                                    setOrderForm(prev => ({ ...prev, paymentProof: null }));
                                  }}
                                  className="absolute top-1 right-1 w-6 h-6 bg-red-500/80 rounded-full flex items-center justify-center hover:bg-red-500"
                                >
                                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>
                            ) : (
                              <>
                                <div className="w-14 h-14 rounded-full bg-teal-400/10 flex items-center justify-center">
                                  <svg className="w-7 h-7 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                  </svg>
                                </div>
                                <span className="text-gray-300 font-medium">Click to upload or drag and drop</span>
                                <span className="text-xs text-teal-400/70">PNG, JPG (Max size: 5MB)</span>
                              </>
                            )}
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3 */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  {/* Success Message */}
                  {orderSuccess && (
                    <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-6 text-center mb-6 animate-fade-in">
                      <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-semibold text-green-400 mb-2">Order Placed Successfully!</h3>
                      <p className="text-gray-300 mb-2">Your order has been submitted successfully.</p>
                      <p className="text-gray-400 text-sm mb-4">Order ID: <span className="text-teal-400 font-medium">{orderId}</span></p>
                      <p className="text-gray-400 text-sm">Redirecting to dashboard in 3 seconds...</p>
                    </div>
                  )}

                  {/* Error Message */}
                  {orderError && (
                    <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-red-400">Error</h3>
                          <div className="mt-1 text-sm text-gray-300">
                            {orderError}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <h3 className="text-xl font-semibold text-white mb-6">Order Summary</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between py-3 border-b border-teal-400/10">
                      <span className="text-gray-400">Challenge Type</span>
                      <span className="text-white font-medium">
                        {orderForm.challengeType === 'instant'
                          ? 'Instant'
                          : orderForm.challengeType === 'oneStep'
                            ? '1-Step Challenge'
                            : orderForm.challengeType === 'twoStep'
                              ? '2-Step Challenge'
                              : '-'}
                      </span>
                    </div>
                    <div className="flex justify-between py-3 border-b border-teal-400/10">
                      <span className="text-gray-400">Account Size</span>
                      <span className="text-white font-medium">
                        {orderForm.accountSize
                          ? `$${parseInt(orderForm.accountSize).toLocaleString('en-US')}`
                          : '-'}
                      </span>
                    </div>
                    <div className="flex justify-between py-3 border-b border-teal-400/10">
                      <span className="text-gray-400">Platform</span>
                      <span className="text-white font-medium">
                        {orderForm.platform === 'mt4'
                          ? 'MetaTrader 4'
                          : orderForm.platform === 'mt5'
                            ? 'MetaTrader 5'
                            : '-'}
                      </span>
                    </div>
                    <div className="flex justify-between py-3 border-b border-teal-400/10">
                      <span className="text-gray-400">Original Price</span>
                      <span className="text-gray-400 line-through">
                        {orderForm.challengeType && orderForm.accountSize
                          ? `$${realPrice}`
                          : '-'}
                      </span>
                    </div>
                    <div className="flex justify-between py-3 border-b border-teal-400/10">
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400">Discount</span>
                        <span className="bg-teal-500/20 text-teal-300 text-xs px-2 py-0.5 rounded">
                          {realPrice > 0 ? `${((savings / realPrice) * 100).toFixed(0)}% OFF` : ''}
                        </span>
                      </div>
                      <span className="text-teal-400">
                        {orderForm.challengeType && orderForm.accountSize
                          ? `-$${savings.toFixed(0)}`
                          : '-'}
                      </span>
                    </div>
                    <div className="flex justify-between py-3">
                      <span className="text-white font-medium">Total</span>
                      <span className="text-teal-400 text-xl font-bold">
                        {orderForm.challengeType && orderForm.accountSize
                          ? `$${discountedPrice}`
                          : '$0'}
                      </span>
                    </div>
                  </div>

                  {/* Trading Rules */}
                  <div className="p-4 bg-slate-800/50 rounded-lg mb-4">
                    <h3 className="text-lg font-semibold text-white mb-4">Trading Rules</h3>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Profit Target Phase 1</span>
                        <span className="text-gray-200">10%</span>
                      </div>

                      {orderForm.challengeType === 'twoStep' && (
                        <div className="flex justify-between">
                          <span className="text-gray-400">Profit Target Phase 2</span>
                          <span className="text-gray-200">5%</span>
                        </div>
                      )}

                      <div className="flex justify-between">
                        <span className="text-gray-400">Max Daily Loss</span>
                        <span className="text-gray-200">
                          {orderForm.challengeType === 'instant' ? '2.5%' : '4%'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Max Total Loss</span>
                        <span className="text-gray-200">
                          {orderForm.challengeType === 'instant' ? '5%' :
                            orderForm.challengeType === 'oneStep' ? '8%' : '10%'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Drawdown Type</span>
                        <span className="text-gray-200">Equity Based</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-end mt-8 pt-4 border-t border-teal-400/10">
                {currentStep > 1 && (
                  <button
                    onClick={handlePrevious}
                    className="px-6 py-3 bg-[#1E293B]/50 hover:bg-[#1E293B] text-teal-400 rounded-xl
                      border border-teal-400/20 transition-all duration-300 hover:border-teal-400/40
                      focus:outline-none focus:ring-2 focus:ring-teal-400/40 mr-4"
                  >
                    Previous Step
                  </button>
                )}
                <button
                  onClick={handleNext}
                  className={`px-6 py-3 bg-teal-400 hover:bg-teal-300 text-[#0B1221] rounded-xl font-medium
                    transition-all duration-300
                    focus:outline-none focus:ring-2 focus:ring-teal-400/40
                    ${(currentStep === 3 && !orderSuccess) ? '' : 'hover:translate-y-[-1px]'}
                    ${isSubmitting || orderSuccess ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={isSubmitting || orderSuccess}
                >
                  {isSubmitting ? 'Processing...' :
                   orderSuccess ? 'Order Placed' :
                   currentStep === 3 ? 'Submit Order' : 'Next Step'}
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PlaceOrderPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-teal-500"></div>
      </div>
    }>
      <PlaceOrderContent />
    </Suspense>
  );
}