'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { authService, directApiCall } from '@/services/api';

interface Order {
  order_id: string;
  challenge_type: string;
  account_size: string;
  platform: string;
  created_at: string;
  server?: string;
  platform_login?: string;
  status?: string;
  profit_share?: number;
}

interface Profile {
  username: string;
  email: string;
  name: string;
  country: string;
  phone_no: string;
  address: string;
}

interface OrdersData {
  pending: Order[];
  active: Order[];
  failed: Order[];
  stage2: Order[];
  live: Order[];
  total_count: number;
}

interface UserData {
  profile: Profile;
  orders: OrdersData;
}

export default function ProfilePage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Get token using authService
        const token = authService.getToken();
        console.log('Token from authService:', token ? 'Token exists' : 'No token');

        if (!token) {
          console.error('No access token found');
          window.location.href = '/login';
          return;
        }

        console.log('Making request to profile endpoint...');

        // Use directApiCall instead of fetch
        const data = await directApiCall('order/user/profile-and-orders', {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        console.log('API Response Data:', {
          hasProfile: !!data?.profile,
          hasOrders: !!data?.orders,
          totalOrders: data?.orders?.total_count,
          orderTypes: data?.orders ? Object.keys(data.orders) : [],
          fullData: data
        });

        setUserData(data);
      } catch (error: any) {
        console.error('Detailed error information:', {
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack,
          isTypeError: error instanceof TypeError,
          isNetworkError: error instanceof TypeError && error.message === 'Failed to fetch',
          isAxiosError: error.isAxiosError,
          axiosResponse: error.response?.data
        });

        if (error?.response?.status === 401) {
          console.error('Unauthorized access - clearing token');
          authService.logout();
          window.location.href = '/login';
          return;
        }

        // Handle other errors
        if (error instanceof TypeError && error.message === 'Failed to fetch') {
          console.error('Network or CORS error occurred');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
          <p className="mt-4 text-teal-500">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
      >
        {/* Profile Card */}
        <motion.div
          variants={itemVariants}
          className="lg:col-span-1"
        >
          <div className="bg-gradient-to-br from-[#0C1424]/95 to-[#0F172A]/95 backdrop-blur-xl rounded-xl overflow-hidden
            shadow-[0_8px_30px_rgba(0,0,0,0.4)] border border-[#1E293B]/40">
            <div className="relative border-b border-[#1E2A4A]/30">
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
                <div className="absolute top-0 right-0 w-[600px] h-24 bg-gradient-to-l from-teal-500/5 via-blue-500/5 to-transparent transform rotate-12 translate-x-20 translate-y-2 opacity-80"></div>
                <div className="absolute top-0 left-0 w-1/3 h-0.5 bg-gradient-to-r from-teal-500/40 to-transparent"></div>
              </div>
              <div className="px-7 py-6 relative">
                <h2 className="text-2xl font-semibold mb-1 bg-gradient-to-r from-white via-teal-100 to-gray-200 bg-clip-text text-transparent">
                  Profile Information
                </h2>
              </div>
            </div>
            <div className="p-7">
              <div className="flex flex-col items-center mb-6">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-teal-500 to-blue-500 p-1 mb-4">
                  <div className="w-full h-full rounded-full bg-gray-900 flex items-center justify-center">
                    <span className="text-3xl font-semibold text-white">
                      {userData?.profile?.name ? userData.profile.name.charAt(0) : 'U'}
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-white mb-1">{userData?.profile?.name || 'User'}</h3>
                <p className="text-gray-400">{userData?.profile?.email || 'No email provided'}</p>
              </div>
              <div className="space-y-4">
                {[
                  { label: "Username", value: userData?.profile?.username || 'Not set', icon: "user" },
                  { label: "Country", value: userData?.profile?.country || 'Not set', icon: "globe" },
                  { label: "Phone", value: userData?.profile?.phone_no || 'Not set', icon: "phone" },
                  { label: "Address", value: userData?.profile?.address || 'Not set', icon: "location" }
                ].map((item, index) => (
                  <div key={index} className="flex items-center p-3 rounded-lg bg-gray-800/30 border border-gray-700/30">
                    <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mr-4">
                      {item.icon === "user" && (
                        <svg className="w-5 h-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      )}
                      {item.icon === "globe" && (
                        <svg className="w-5 h-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                        </svg>
                      )}
                      {item.icon === "phone" && (
                        <svg className="w-5 h-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                      )}
                      {item.icon === "location" && (
                        <svg className="w-5 h-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">{item.label}</p>
                      <p className="text-white font-medium">{item.value}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Orders Summary */}
        <motion.div
          variants={itemVariants}
          className="lg:col-span-2"
        >
          <div className="bg-gradient-to-br from-[#0C1424]/95 to-[#0F172A]/95 backdrop-blur-xl rounded-xl overflow-hidden
            shadow-[0_8px_30px_rgba(0,0,0,0.4)] border border-[#1E293B]/40">
            <div className="relative border-b border-[#1E2A4A]/30">
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
                <div className="absolute top-0 right-0 w-[600px] h-24 bg-gradient-to-l from-teal-500/5 via-blue-500/5 to-transparent transform rotate-12 translate-x-20 translate-y-2 opacity-80"></div>
                <div className="absolute top-0 left-0 w-1/3 h-0.5 bg-gradient-to-r from-teal-500/40 to-transparent"></div>
              </div>
              <div className="px-7 py-6 relative">
                <h2 className="text-2xl font-semibold mb-1 bg-gradient-to-r from-white via-teal-100 to-gray-200 bg-clip-text text-transparent">
                  Trading History
                </h2>
                <p className="text-gray-400 text-sm">Total Orders: {userData?.orders?.total_count || 0}</p>
              </div>
            </div>
            <div className="p-7">
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {[
                  { label: "Active Orders", orders: userData?.orders.active, color: "from-teal-500/20" },
                  { label: "Stage 2", orders: userData?.orders.stage2, color: "from-blue-500/20" },
                  { label: "Live Accounts", orders: userData?.orders.live, color: "from-purple-500/20" },
                  { label: "Failed Challenges", orders: userData?.orders.failed, color: "from-red-500/20" },
                  { label: "Pending Orders", orders: userData?.orders.pending, color: "from-yellow-500/20" }
                ].map((category, index) => (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className={`bg-gradient-to-br ${category.color} to-transparent backdrop-blur-sm rounded-xl p-5 border border-gray-700/30`}
                  >
                    <h3 className="text-lg font-medium text-white mb-3">{category.label}</h3>
                    <div className="space-y-3">
                      {category.orders && category.orders.length > 0 ? (
                        category.orders.map((order: Order, orderIndex: number) => (
                          <div key={orderIndex} className="bg-gray-800/30 rounded-lg p-3">
                            <div className="flex justify-between items-start mb-2">
                              <span className="text-sm font-medium text-teal-400">{order.order_id}</span>
                              <span className="text-xs text-gray-400">
                                {new Date(order.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div>
                                <p className="text-gray-400">Type</p>
                                <p className="text-white">{order.challenge_type}</p>
                              </div>
                              <div>
                                <p className="text-gray-400">Size</p>
                                <p className="text-white">${order.account_size}</p>
                              </div>
                              <div>
                                <p className="text-gray-400">Platform</p>
                                <p className="text-white">{order.platform.toUpperCase()}</p>
                              </div>
                              {order.profit_share && (
                                <div>
                                  <p className="text-gray-400">Profit Share</p>
                                  <p className="text-white">{order.profit_share}%</p>
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-400 text-sm">No orders</p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}