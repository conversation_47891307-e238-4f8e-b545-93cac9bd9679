'use client';

import { motion } from 'framer-motion';

export default function WithdrawalPage() {
  return (
    <div className="w-full h-full p-0 m-0">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mx-4 my-6 bg-[#0F1A2E]/90 backdrop-blur-2xl rounded-2xl border border-teal-400/20 overflow-hidden
          shadow-[0_0_40px_-15px_rgba(45,212,191,0.1)] hover:shadow-[0_0_50px_-12px_rgba(45,212,191,0.2)] transition-all duration-500"
      >
        <div className="p-8 flex flex-col items-center justify-center text-center">
          {/* Lock Icon */}
          <div className="w-20 h-20 rounded-full bg-[#0B1221] flex items-center justify-center mb-6 border border-teal-400/20">
            <svg className="w-10 h-10 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 8l3 5m0 0l3-5m-3 5v4m-3-5h6m-6 3h6m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
      </div>
      
          {/* Message */}
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Withdrawals Locked</h2>
          
          {/* Decorative Line */}
          <div className="w-24 h-1 bg-gradient-to-r from-teal-400 to-teal-600 rounded-full mb-4"></div>
          
          <p className="text-gray-400 max-w-lg mb-6">
            Complete KYC verification to be eligible for withdrawals. Once your identity is verified, you'll be able to request withdrawals and access your funds.
          </p>
          
          {/* Steps */}
          <div className="w-full max-w-md flex items-center justify-between mb-8">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-teal-400/10 border border-teal-400 flex items-center justify-center mb-2">
                <span className="text-teal-400 font-medium">1</span>
              </div>
              <span className="text-xs text-gray-500">Challenge</span>
            </div>
            
            <div className="h-0.5 flex-grow bg-[#0B1221]">
              <div className="bg-gradient-to-r from-teal-400 to-teal-600 h-0.5 w-full"></div>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-[#0B1221] border border-gray-800 flex items-center justify-center mb-2">
                <span className="text-gray-500 font-medium">2</span>
              </div>
              <span className="text-xs text-gray-500">KYC</span>
        </div>
        
            <div className="h-0.5 flex-grow bg-[#0B1221]"></div>
            
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-full bg-[#0B1221] border border-gray-800 flex items-center justify-center mb-2">
                <span className="text-gray-500 font-medium">3</span>
              </div>
              <span className="text-xs text-gray-500">Withdraw</span>
                    </div>
                  </div>
                  
          {/* Locked Button */}
          <button
            disabled
            className="bg-[#0B1221] text-gray-500 font-medium px-8 py-3 rounded-xl border border-gray-800 flex items-center space-x-2 cursor-not-allowed"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
            <span>Withdrawals Locked</span>
                </button>
          
          {/* Background Glow */}
          <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-teal-400/5 rounded-full blur-[100px] pointer-events-none"></div>
          <div className="absolute -top-40 -left-40 w-80 h-80 bg-teal-400/5 rounded-full blur-[100px] pointer-events-none"></div>
        </div>
      </motion.div>
    </div>
  );
} 