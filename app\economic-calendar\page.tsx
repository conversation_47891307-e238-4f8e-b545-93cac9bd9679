"use client";
import React, { useEffect, useRef } from "react";

export default function EconomicCalendarPage() {
  const widgetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!widgetRef.current) return;
    // Remove any previous widget
    widgetRef.current.innerHTML = "";
    const script = document.createElement("script");
    script.src = "https://s3.tradingview.com/external-embedding/embed-widget-events.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = JSON.stringify({
      colorTheme: "dark",
      isTransparent: false,
      width: "100%",
      height: 700,
      locale: "en",
      importanceFilter: "-1,0,1"
    });
    widgetRef.current.appendChild(script);
  }, []);

  return (
    <div className="min-h-screen bg-[#030609] relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200">
      {/* Background elements matching landing page */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-blue-500/10 to-teal-500/10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#030609]/20 to-[#030609]/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center relative">
            {/* Decorative elements */}
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-teal-400 to-transparent opacity-60"></div>
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-40"></div>
            
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-500/20 to-blue-500/20 border border-teal-500/30 text-teal-300 text-sm font-medium mb-6 backdrop-blur-sm">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Market Analysis
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white via-teal-100 to-white bg-clip-text text-transparent">
                Economic
              </span>
              <br />
              <span className="bg-gradient-to-r from-teal-400 via-blue-400 to-teal-400 bg-clip-text text-transparent">
                Calendar
          </span>
        </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light">
              Stay informed with real-time 
              <span className="text-teal-400 font-medium"> economic events</span> and 
              <span className="text-blue-400 font-medium"> market-moving news</span> that can impact your 
              <span className="text-teal-400 font-medium"> trading decisions</span>.
            </p>
            
            {/* Stats */}
            <div className="flex justify-center items-center space-x-8 mt-12 pt-8 border-t border-gray-700/30">
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">24/7</div>
                <div className="text-sm text-gray-400">Live Updates</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">Global</div>
                <div className="text-sm text-gray-400">Markets</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">Real-time</div>
                <div className="text-sm text-gray-400">Impact</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* TradingView Widget */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
        <div className="bg-[#101C2C] rounded-2xl border border-gray-700/50 shadow-xl backdrop-blur-sm overflow-hidden">
          <div id="tradingview-widget" className="w-full h-[600px]"></div>
        </div>
      </div>
    </div>
  );
} 