'use client';

import { useState } from 'react';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

// Simple SVG icons
const ChevronDownIcon = () => (
  <svg className="h-5 w-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const ChevronUpIcon = () => (
  <svg className="h-5 w-5 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
  </svg>
);

const faqData: FAQItem[] = [
  {
    question: "What is FXentra and how does it work?",
    answer: "FXentra is a comprehensive trading platform that provides access to global financial markets including forex, cryptocurrencies, stocks, and commodities. Our platform combines advanced trading technology with user-friendly interfaces, offering both MetaTrader 4 and MetaTrader 5 support. Users can trade with leverage, access real-time market data, and utilize advanced charting tools to make informed trading decisions.",
    category: "Platform"
  },
  {
    question: "How do I get started with trading on FXentra?",
    answer: "Getting started is simple! First, create an account by providing your basic information and completing the verification process. Once verified, you can fund your account using various payment methods including bank transfers, credit cards, and cryptocurrency. After funding, you can access our trading platforms and start trading immediately with our demo account or real trading account.",
    category: "Getting Started"
  },
  {
    question: "What trading platforms do you support?",
    answer: "We support both MetaTrader 4 (MT4) and MetaTrader 5 (MT5), the industry's most popular trading platforms. MT4 is perfect for forex trading with its user-friendly interface and extensive indicator library. MT5 offers advanced features including more timeframes, additional order types, and access to stocks and futures markets. Both platforms are available on desktop, web, and mobile devices.",
    category: "Platform"
  },
  {
    question: "What are the minimum deposit requirements?",
    answer: "Our minimum deposit requirement is $250 for standard accounts. However, we offer various account types with different minimum deposits: Basic ($250), Standard ($1,000), Premium ($5,000), and VIP ($25,000). Each account type comes with different benefits including lower spreads, higher leverage, and dedicated account managers.",
    category: "Account"
  },
  {
    question: "What leverage do you offer?",
    answer: "We offer flexible leverage options ranging from 1:1 to 1:500, depending on your account type and the financial instrument you're trading. Forex pairs typically offer up to 1:500 leverage, while cryptocurrencies offer up to 1:100, and stocks offer up to 1:20. Higher leverage means higher potential returns but also increased risk.",
    category: "Trading"
  },
  {
    question: "How do I withdraw my profits?",
    answer: "Withdrawals can be processed through the same method you used for deposits. We support bank transfers, credit/debit cards, and cryptocurrency withdrawals. Processing times vary: bank transfers take 3-5 business days, card withdrawals take 1-3 business days, and cryptocurrency withdrawals are typically processed within 24 hours. There are no withdrawal fees for most methods.",
    category: "Account"
  },
  {
    question: "What spreads do you offer?",
    answer: "Our spreads start from as low as 0.1 pips for major forex pairs like EUR/USD. Spreads vary by account type: Basic accounts start at 1.2 pips, Standard accounts at 0.8 pips, Premium accounts at 0.3 pips, and VIP accounts at 0.1 pips. We offer competitive spreads across all major currency pairs, cryptocurrencies, and other instruments.",
    category: "Trading"
  },
  {
    question: "Do you offer demo accounts?",
    answer: "Yes! We provide free demo accounts with $10,000 virtual balance so you can practice trading without risking real money. Demo accounts have access to all the same features as live accounts including real-time market data, all trading instruments, and full platform functionality. You can use the demo account indefinitely to develop your trading strategy.",
    category: "Getting Started"
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept multiple payment methods including bank transfers, credit/debit cards (Visa, Mastercard), and various cryptocurrencies (Bitcoin, Ethereum, USDT, and others). All deposits are processed securely and typically credited to your account within 24 hours. We don't charge any deposit fees, though your bank or payment processor may apply their own fees.",
    category: "Account"
  },
  {
    question: "Is my money safe with FXentra?",
    answer: "Absolutely! We prioritize the security of your funds and personal information. We use bank-grade encryption, secure servers, and follow strict regulatory compliance. Your funds are held in segregated accounts at top-tier banks, separate from our operational funds. We're regulated by financial authorities and maintain comprehensive insurance coverage for client funds.",
    category: "Security"
  },
  {
    question: "What trading instruments are available?",
    answer: "We offer a comprehensive range of trading instruments including 50+ forex pairs, major cryptocurrencies (Bitcoin, Ethereum, Litecoin, etc.), stocks from major exchanges (NYSE, NASDAQ, LSE), commodities (gold, silver, oil), and indices (S&P 500, NASDAQ, FTSE 100). New instruments are regularly added based on market demand.",
    category: "Trading"
  },
  {
    question: "Do you provide educational resources?",
    answer: "Yes! We offer extensive educational resources including video tutorials, webinars, trading guides, market analysis, and a comprehensive knowledge base. Our educational content covers everything from basic trading concepts to advanced strategies. We also provide daily market updates and economic calendar information to help you make informed trading decisions.",
    category: "Education"
  },
  {
    question: "What customer support do you offer?",
    answer: "We provide 24/7 customer support through multiple channels including live chat, email, and phone support. Our multilingual support team is available in English, Spanish, French, German, and Arabic. We also offer dedicated account managers for Premium and VIP clients, ensuring personalized assistance for all your trading needs.",
    category: "Support"
  },
  {
    question: "Can I trade on mobile devices?",
    answer: "Yes! Our platforms are fully compatible with mobile devices. You can download MT4 and MT5 mobile apps for iOS and Android devices. The mobile apps offer the same functionality as desktop versions including real-time charts, order management, and market analysis. You can trade anywhere, anytime with our mobile trading solutions.",
    category: "Platform"
  },
  {
    question: "What are the trading hours?",
    answer: "Forex markets are open 24/5 (Monday to Friday), while cryptocurrency markets trade 24/7. Stock markets follow their respective exchange hours. Our platform is available for trading during all market hours, and you can place orders outside market hours that will be executed when markets reopen. Real-time market data is available 24/7.",
    category: "Trading"
  },
  {
    question: "Do you offer copy trading?",
    answer: "Yes! We offer copy trading services where you can automatically copy the trades of successful traders. Our copy trading platform allows you to browse and select from verified traders, set your risk parameters, and automatically replicate their trading strategies. This is perfect for beginners or those who prefer a more passive trading approach.",
    category: "Trading"
  }
];

const categories = ['All', 'Platform', 'Getting Started', 'Account', 'Trading', 'Security', 'Education', 'Support'];

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('All');

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(item => item !== index)
        : [...prev, index]
    );
  };

  const filteredFAQs = selectedCategory === 'All' 
    ? faqData 
    : faqData.filter(faq => faq.category === selectedCategory);

  return (
    <div className="min-h-screen bg-[#030609] relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200">
      {/* Background elements matching landing page */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-blue-500/10 to-teal-500/10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#030609]/20 to-[#030609]/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center relative">
            {/* Decorative elements */}
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-teal-400 to-transparent opacity-60"></div>
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-40"></div>
            
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-500/20 to-blue-500/20 border border-teal-500/30 text-teal-300 text-sm font-medium mb-6 backdrop-blur-sm">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              Support Center
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white via-teal-100 to-white bg-clip-text text-transparent">
                Frequently Asked
              </span>
              <br />
              <span className="bg-gradient-to-r from-teal-400 via-blue-400 to-teal-400 bg-clip-text text-transparent">
                Questions
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light">
              Find comprehensive answers to the most common questions about our 
              <span className="text-teal-400 font-medium"> trading platform</span>, 
              <span className="text-blue-400 font-medium"> account management</span>, and 
              <span className="text-teal-400 font-medium"> trading services</span>.
            </p>
            
            {/* Stats */}
            <div className="flex justify-center items-center space-x-8 mt-12 pt-8 border-t border-gray-700/30">
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">15+</div>
                <div className="text-sm text-gray-400">Categories</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">50+</div>
                <div className="text-sm text-gray-400">Questions</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">24/7</div>
                <div className="text-sm text-gray-400">Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12 relative z-10">
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                selectedCategory === category
                  ? 'bg-teal-500 text-white shadow-lg shadow-teal-500/25'
                  : 'bg-[#101C2C] text-gray-300 hover:bg-[#131B29] hover:text-white border border-gray-700/50'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* FAQ Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-24 relative z-10">
        <div className="space-y-4">
          {filteredFAQs.map((faq, index) => (
            <div
              key={index}
              className="bg-[#101C2C] backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden hover:border-teal-500/20 hover:shadow-teal-500/10 transition-all duration-300"
            >
                      <button
                onClick={() => toggleItem(index)}
                className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-[#131B29]/50 transition-colors duration-200"
              >
                <h3 className="text-lg font-semibold text-white pr-4">
                  {faq.question}
                </h3>
                <div className="flex-shrink-0">
                  {openItems.includes(index) ? (
                    <ChevronUpIcon />
                  ) : (
                    <ChevronDownIcon />
                  )}
                        </div>
                      </button>
              
              {openItems.includes(index) && (
                <div className="px-6 pb-6">
                  <div className="pt-4 border-t border-gray-700/50">
                    <p className="text-gray-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
          </div>

        {/* Contact Section */}
        <div className="mt-16 text-center">
          <div className="bg-[#101C2C] backdrop-blur-sm border border-gray-700/50 rounded-xl p-8 hover:border-teal-500/20 transition-all duration-300">
            <h3 className="text-2xl font-bold text-white mb-4">
              Still Have Questions?
            </h3>
            <p className="text-gray-300 mb-6">
              Can't find the answer you're looking for? Our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-3 bg-teal-500 hover:bg-teal-600 text-white font-semibold rounded-lg transition-colors duration-200 shadow-lg shadow-teal-500/25">
                Contact Support
              </button>
              <button className="px-8 py-3 bg-[#131B29] hover:bg-[#1A2332] text-white font-semibold rounded-lg transition-colors duration-200 border border-gray-700/50">
                Live Chat
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 
