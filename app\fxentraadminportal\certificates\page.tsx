'use client';

import { useState, useEffect } from 'react';
import { Order, Certificate, adminService } from '@/services/adminService';
import OrdersTable from '@/components/admin/OrdersTable';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import EditOrderModal from '@/components/admin/EditOrderModal';
import CertificateGenerator from '@/components/admin/CertificateGenerator';

export default function CertificatesPage() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [passedOrders, setPassedOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);

  // Fetch certificates and passed orders
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const [certificatesData, passedOrdersData] = await Promise.all([
          adminService.getCertificates(),
          adminService.getPassedOrders()
        ]);

        setCertificates(certificatesData);
        setPassedOrders(passedOrdersData);
      } catch (error) {
        setError('Failed to load certificates. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle order update
  const handleOrderUpdated = (updatedOrder: Order) => {
    // Update the orders list with the updated order
    setPassedOrders(prevOrders =>
      prevOrders.map(order => order.id === updatedOrder.id ? updatedOrder : order)
    );
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsEditModalOpen(true);
  };

  // Handle generate certificate
  const handleGenerateCertificate = (order: Order) => {
    setSelectedOrder(order);
    setIsCertificateModalOpen(true);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Certificates</h1>
          <p className="text-gray-400">View and manage certificates for passed accounts</p>
        </div>
        <button
          onClick={() => {
            setIsLoading(true);
            setError(null);
            Promise.all([
              adminService.getCertificates(),
              adminService.getPassedOrders()
            ])
              .then(([certificatesData, passedOrdersData]) => {
                setCertificates(certificatesData);
                setPassedOrders(passedOrdersData);
                setIsLoading(false);
              })
              .catch(() => {
                setError('Failed to refresh certificates. Please try again.');
                setIsLoading(false);
              });
          }}
          disabled={isLoading}
          className="px-4 py-2 bg-teal-500/20 hover:bg-teal-500/30 rounded-md text-white flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-teal-500">Loading certificate data...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400">{error}</p>
          <button
            onClick={() => {
              setIsLoading(true);
              setError(null);
              Promise.all([
                adminService.getCertificates(),
                adminService.getPassedOrders()
              ])
                .then(([certificatesData, passedOrdersData]) => {
                  setCertificates(certificatesData);
                  setPassedOrders(passedOrdersData);
                  setIsLoading(false);
                })
                .catch(() => {
                  setError('Failed to load certificates. Please try again later.');
                  setIsLoading(false);
                });
            }}
            className="mt-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded-md text-white transition-colors"
          >
            Retry
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Existing Certificates */}
          <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
            <div className="px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
              <h2 className="text-lg font-medium text-white">Existing Certificates</h2>
              <span className="px-2 py-1 bg-teal-500/20 rounded-full text-xs text-teal-400">
                {certificates.length} total
              </span>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-teal-500/20">
                <thead className="bg-[#070F1B]">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Certificate Number
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Account Size
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Challenge Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Issue Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Profit Target
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-teal-500/20">
                  {certificates.length > 0 ? (
                    certificates.map((certificate) => (
                      <tr key={certificate.certificate_number} className="hover:bg-[#070F1B]/50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                          {certificate.certificate_number}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {certificate.order_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {certificate.username}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {certificate.account_size}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {certificate.challenge_type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {certificate.issue_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {certificate.profit_target}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => {
                              // Find the corresponding order
                              const order = passedOrders.find(o => o.order_id === certificate.order_id);
                              if (order) {
                                setSelectedOrder(order);
                                setIsViewModalOpen(true);
                              } else {
                                alert(`No order details found for certificate ${certificate.certificate_number}`);
                              }
                            }}
                            className="text-teal-400 hover:text-teal-300 mr-3 px-2 py-1 rounded-md hover:bg-teal-500/10 transition-colors"
                          >
                            <span className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                              View
                            </span>
                          </button>
                          <button
                            onClick={() => {
                              window.open(`/admin/certificates/print/${certificate.certificate_number}`, '_blank');
                            }}
                            className="text-teal-400 hover:text-teal-300 px-2 py-1 rounded-md hover:bg-teal-500/10 transition-colors"
                          >
                            <span className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                              </svg>
                              Print
                            </span>
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="px-6 py-4 text-center text-sm text-gray-400">
                        No certificates found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Eligible Accounts for Certificates */}
          <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
            <div className="px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
              <h2 className="text-lg font-medium text-white">Eligible Accounts for Certificates</h2>
              <span className="px-2 py-1 bg-teal-500/20 rounded-full text-xs text-teal-400">
                {passedOrders.length} total
              </span>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-teal-500/20">
                <thead className="bg-[#070F1B]">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Account Size
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Platform
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Date Passed
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-teal-500/20">
                  {passedOrders.length > 0 ? (
                    passedOrders.map((order) => (
                      <tr key={order.id} className="hover:bg-[#070F1B]/50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                          {order.order_id || order.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          <div>{order.username}</div>
                          <div className="text-xs text-gray-400">{order.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {order.account_size}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {order.platform}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 text-xs rounded-full bg-blue-500/20 text-blue-400">
                            Passed
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {order.created_at ? new Date(order.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleViewOrder(order)}
                            className="text-teal-400 hover:text-teal-300 mr-3 px-2 py-1 rounded-md hover:bg-teal-500/10 transition-colors"
                          >
                            <span className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                              View
                            </span>
                          </button>
                          <button
                            onClick={() => handleGenerateCertificate(order)}
                            className="text-teal-400 hover:text-teal-300 px-2 py-1 rounded-md hover:bg-teal-500/10 transition-colors"
                          >
                            <span className="flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              Generate Certificate
                            </span>
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-400">
                        No passed orders found for certificate generation
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* View Order Modal */}
      <ViewOrderModal
        order={selectedOrder}
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        onEdit={handleEditOrder}
      />

      {/* Edit Order Modal */}
      <EditOrderModal
        order={selectedOrder}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onOrderUpdated={handleOrderUpdated}
      />

      {/* Certificate Generator Modal */}
      {selectedOrder && isCertificateModalOpen && (
        <CertificateGenerator
          order={selectedOrder}
          onClose={() => setIsCertificateModalOpen(false)}
        />
      )}
    </div>
  );
}
