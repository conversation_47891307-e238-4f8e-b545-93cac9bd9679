'use client';

import { useState, useEffect } from 'react';
import { adminService, ContainerAccount } from '@/services/adminService';
import AddContainerModal from '@/components/admin/AddContainerModal';

const accountTypeOptions = [
  { label: 'INSTANT', value: 'instant' },
  { label: 'Phase 1', value: 'phase1' },
  { label: 'Phase 2', value: 'phase2' },
  { label: 'Live', value: 'live' }
];

const statusTypes = [
  { label: 'All Status', value: 'all' },
  { label: 'Active', value: 'active' },
  { label: 'Pending', value: 'pending' },
  { label: 'Inactive', value: 'inactive' },
  { label: 'Error', value: 'error' },
];

// Filter Button Component
const FilterButton = ({
  label,
  isSelected,
  onClick
}: {
  label: string;
  isSelected: boolean;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
      isSelected
        ? 'bg-teal-500/20 text-teal-400 border border-teal-500/30'
        : 'text-gray-400 hover:bg-teal-500/10 hover:text-teal-300'
    }`}
  >
    {label}
  </button>
);

export default function ContainersPage() {
  const [containers, setContainers] = useState<ContainerAccount[]>([]);
  const [filteredContainers, setFilteredContainers] = useState<ContainerAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Fetch containers
  useEffect(() => {
    const fetchContainers = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await adminService.getContainers();
        setContainers(data);
        setFilteredContainers(data);
      } catch (error: any) {
        setError(error.message || 'Failed to load containers. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchContainers();
  }, []);

  // Filter containers based on search term, type, and status
  useEffect(() => {
    let filtered = [...containers];

    // Filter by account type
    if (selectedType !== 'all') {
      filtered = filtered.filter(container => container.account_type?.toLowerCase() === selectedType);
    }

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(container => container.status?.toLowerCase() === selectedStatus);
    }

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(container =>
        container.platform_login?.toLowerCase().includes(searchLower) ||
        container.server?.toLowerCase().includes(searchLower) ||
        container.account_size?.includes(searchTerm) ||
        (container.order_id && container.order_id.toString().includes(searchTerm))
      );
    }

    setFilteredContainers(filtered);
  }, [containers, selectedType, selectedStatus, searchTerm]);

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status color
  const getStatusColor = (status: string | undefined) => {
    const colors = {
      active: 'bg-green-500/20 border-green-500/30 text-green-400',
      inactive: 'bg-gray-500/20 border-gray-500/30 text-gray-400',
      pending: 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400',
      error: 'bg-red-500/20 border-red-500/30 text-red-400'
    };
    return colors[(status || 'inactive').toLowerCase() as keyof typeof colors] || colors.inactive;
  };

  // Handle new container added
  const handleContainerAdded = (newContainer: ContainerAccount) => {
    setContainers(prev => [...prev, newContainer]);
  };

  return (
    <div className="px-6 pb-6">
      {/* Header */}
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Container Accounts</h1>
          <p className="text-gray-400">View and manage trading container accounts</p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="px-4 py-2 bg-teal-600 text-white text-sm font-medium rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 flex items-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New Account
        </button>
      </div>

      {/* Filters and Search */}
      <div className="space-y-4 mb-6">
        {/* Search Bar */}
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-4">
          <div className="relative w-full sm:w-96">
            <input
              type="text"
              placeholder="Search by login, server, size, or order ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-teal-500"
            />
            <svg
              className="absolute right-3 top-2.5 h-4 w-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Account Type Filters */}
          <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-4">
            <h3 className="text-sm font-medium text-gray-400 mb-3">Account Type</h3>
            <div className="flex flex-wrap gap-2">
              {accountTypeOptions.map((type) => (
                <FilterButton
                  key={type.value}
                  label={type.label}
                  isSelected={selectedType === type.value}
                  onClick={() => setSelectedType(type.value)}
                />
              ))}
            </div>
          </div>

          {/* Status Filters */}
          <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-4">
            <h3 className="text-sm font-medium text-gray-400 mb-3">Status</h3>
            <div className="flex flex-wrap gap-2">
              {statusTypes.map((status) => (
                <FilterButton
                  key={status.value}
                  label={status.label}
                  isSelected={selectedStatus === status.value}
                  onClick={() => setSelectedStatus(status.value)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-teal-500">Loading containers...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 text-red-400">
          {error}
        </div>
      ) : (
        <>
          {/* Results Summary */}
          <div className="mb-4 text-sm text-gray-400">
            Showing {filteredContainers.length} of {containers.length} containers
          </div>

          {/* Container Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredContainers.map((container) => (
              <div
                key={container.id}
                className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden hover:border-teal-500/40 transition-colors"
              >
                {/* Card Header */}
                <div className="px-6 py-4 border-b border-teal-500/20">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {(container.platform || 'MT5').toUpperCase()} Account
                      </h3>
                      <p className="text-sm text-gray-400">ID: {container.id}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(container.status)}`}>
                      {container.status || 'Inactive'}
                    </span>
                  </div>
                </div>

                {/* Card Content */}
                <div className="px-6 py-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-400">Server</p>
                      <p className="text-sm text-white">{container.server || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Account Size</p>
                      <p className="text-sm text-white">${container.account_size || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Login</p>
                      <p className="text-sm text-white">{container.platform_login || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Type</p>
                      <p className="text-sm text-white capitalize">{container.account_type || 'N/A'}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-400">Order ID</p>
                    <p className="text-sm text-white">{container.order_id || 'Not assigned'}</p>
                  </div>

                  <div className="pt-4 border-t border-teal-500/20">
                    <div className="flex justify-between items-center text-xs text-gray-400">
                      <span>Created: {formatDate(container.created_at)}</span>
                      <span>Updated: {formatDate(container.updated_at)}</span>
                    </div>
                  </div>
                </div>

                {/* Card Actions */}
                <div className="bg-[#070F1B] px-6 py-3 border-t border-teal-500/20">
                  <div className="flex justify-end space-x-3">
                    <button className="px-3 py-1 text-sm text-teal-400 hover:text-teal-300 transition-colors">
                      View Details
                    </button>
                    <button className="px-3 py-1 text-sm text-blue-400 hover:text-blue-300 transition-colors">
                      Edit
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {/* Add Container Modal */}
      <AddContainerModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onContainerAdded={handleContainerAdded}
      />
    </div>
  );
}