'use client';

import { useState, useEffect } from 'react';
import { AdminDashboardSummary, adminService } from '@/services/adminService';
import DashboardSummary from '@/components/admin/DashboardSummary';
import { fakeData } from '@/lib/fakeData';

export default function AdminDashboardPage() {
  const [summary, setSummary] = useState<AdminDashboardSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch dashboard summary
  useEffect(() => {
    const fetchSummary = async () => {
      setIsLoading(true);
      try {
        // Use fake data instead of API call
        setSummary(fakeData.dashboardSummary);
      } catch (error) {
        // Error handling without logging
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, []);

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Admin Dashboard</h1>
        <p className="text-gray-400">Overview of all orders and users</p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-teal-500">Loading dashboard data...</p>
          </div>
        </div>
      ) : summary ? (
        <DashboardSummary summary={summary} />
      ) : (
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 text-red-400">
          Error loading dashboard data. Please try again later.
        </div>
      )}
    </div>
  );
}
