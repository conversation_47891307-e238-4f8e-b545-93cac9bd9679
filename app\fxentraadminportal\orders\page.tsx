'use client';

import { useState, useEffect } from 'react';
import { Order, adminService } from '@/services/adminService';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import EditOrderModal from '@/components/admin/EditOrderModal';

const orderTypes = [
  { label: 'All Orders', value: 'all', color: 'teal' },
  { label: 'Pending', value: 'pending', color: 'gray' },
  { label: 'Running', value: 'running', color: 'teal' },
  { label: 'Completed', value: 'completed', color: 'blue' },
  { label: 'Failed', value: 'failed', color: 'red' },
  { label: 'Passed', value: 'passed', color: 'green' },
  { label: 'Stage 2', value: 'stage_2', color: 'purple' },
  { label: 'Live', value: 'live', color: 'yellow' },
];

// Utility functions
const getStatusColor = (status: string) => {
  const colors = {
    incomplete: 'bg-gray-500/20 text-gray-300',
    completed: 'bg-blue-500/20 text-blue-300',
    failed: 'bg-red-500/20 text-red-300',
    passed: 'bg-green-500/20 text-green-300',
    stage_2: 'bg-purple-500/20 text-purple-300',
    live: 'bg-yellow-500/20 text-yellow-300',
    running: 'bg-teal-500/20 text-teal-300'
  };
  return colors[status as keyof typeof colors] || colors.incomplete;
};

const formatDate = (date: string | null) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Summary Card Component
const SummaryCard = ({ type, count, isSelected, onClick }: {
  type: typeof orderTypes[0],
  count: number,
  isSelected: boolean,
  onClick: () => void
}) => (
  <button
    onClick={onClick}
    className={`relative flex flex-col items-start p-4 rounded-xl border transition-all duration-200 ${
      isSelected
        ? `bg-${type.color}-500/20 border-${type.color}-500/30 shadow-lg shadow-${type.color}-500/10`
        : 'bg-gray-900/50 border-gray-800/30 hover:bg-gray-900/80'
    }`}
  >
    <div className="flex items-center gap-2">
      <span className={`text-${type.color}-400 text-sm font-medium`}>{type.label}</span>
      {isSelected && (
        <span className="flex h-2 w-2">
          <span className={`animate-ping absolute inline-flex h-2 w-2 rounded-full bg-${type.color}-400 opacity-75`}></span>
          <span className={`relative inline-flex rounded-full h-2 w-2 bg-${type.color}-500`}></span>
        </span>
      )}
    </div>
    <div className="mt-2 flex items-baseline gap-1">
      <span className={`text-2xl font-bold text-${type.color}-300`}>{count}</span>
      <span className="text-sm text-gray-400">orders</span>
    </div>
  </button>
);

export default function UnifiedOrdersPage() {
  // State declarations
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [orderCounts, setOrderCounts] = useState<Record<string, number>>({});
  const ordersPerPage = 20;

  // Event handlers
  const handleOrderUpdated = (updatedOrder: Order) => {
    setOrders((prevOrders: Order[]) =>
      prevOrders.map((order: Order) =>
        (order.id === updatedOrder.id || order.order_id === updatedOrder.order_id)
          ? updatedOrder
          : order
      )
    );
  };

  // Fetch all orders
  const fetchAllOrders = async () => {
    setIsLoading(true);
    try {
      const [
        allOrders,
        pendingOrders,
        runningOrders,
        completedOrders,
        failedOrders,
        passedOrders,
        stage2Orders,
        liveOrders
      ] = await Promise.all([
        adminService.getAllOrders(),
        adminService.getPendingOrders(),
        adminService.getRunningOrders(),
        adminService.getCompletedOrders(),
        adminService.getFailedOrders(),
        adminService.getPassedOrders(),
        adminService.getStage2Orders(),
        adminService.getLiveOrders()
      ]);

      // Combine and deduplicate orders based on order_id
      const orderMap = new Map();
      [...allOrders, ...pendingOrders, ...runningOrders, ...completedOrders,
       ...failedOrders, ...passedOrders, ...stage2Orders, ...liveOrders]
        .forEach(order => {
          const key = order.order_id || order.id;
          orderMap.set(key, order);
        });

      const allOrdersArray = Array.from(orderMap.values());
      setOrders(allOrdersArray);

      // Calculate counts for each order type
      const counts = {
        all: allOrdersArray.length,
        pending: pendingOrders.length,
        running: runningOrders.length,
        completed: completedOrders.length,
        failed: failedOrders.length,
        passed: passedOrders.length,
        stage_2: stage2Orders.length,
        live: liveOrders.length,
      };
      setOrderCounts(counts);
    } catch (error) {
      // Error handling without logging
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllOrders();
  }, []);

  // Filter and sort orders
  useEffect(() => {
    let filtered = [...orders];

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(order => {
        if (selectedType === 'pending') return !order.status || order.status === 'incomplete';
        return order.status === selectedType;
      });
    }

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(order =>
        (order.id && order.id.toString().toLowerCase().includes(searchLower)) ||
        (order.order_id && order.order_id.toString().toLowerCase().includes(searchLower)) ||
        (order.username && order.username.toLowerCase().includes(searchLower)) ||
        (order.email && order.email.toLowerCase().includes(searchLower)) ||
        (order.platform && order.platform.toLowerCase().includes(searchLower)) ||
        (order.account_size && order.account_size.toLowerCase().includes(searchLower))
      );
    }

    // Sort by date (most recent first)
    filtered.sort((a, b) => {
      const dateA = new Date(a.created_at || 0);
      const dateB = new Date(b.created_at || 0);
      return dateB.getTime() - dateA.getTime();
    });

    setFilteredOrders(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [orders, selectedType, searchTerm]);

  // Pagination calculations
  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

  return (
    <div className="px-6 pb-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Orders Management</h1>
        <p className="text-gray-400">View and manage all orders in one place</p>
      </div>

      {/* Summary Cards Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-8 gap-4 mb-8">
        {orderTypes.map((type) => (
          <SummaryCard
            key={type.value}
            type={type}
            count={orderCounts[type.value] || 0}
            isSelected={selectedType === type.value}
            onClick={() => setSelectedType(type.value)}
          />
        ))}
      </div>

      {/* Search Bar */}
      <div className="mb-6 bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-4">
        <div className="relative w-full sm:w-96">
          <input
            type="text"
            placeholder="Search orders by ID, username, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-teal-500"
          />
          <svg
            className="absolute right-3 top-2.5 h-4 w-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
              <p className="mt-4 text-teal-500">Loading orders...</p>
            </div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-teal-500/20">
                <thead className="bg-[#070F1B]">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Account Size
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Platform
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-teal-500/20">
                  {currentOrders.map((order) => (
                    <tr key={order.id} className="hover:bg-[#070F1B]/50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className="text-teal-400">{order.order_id || order.id}</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <span className="text-sm text-white">{order.username}</span>
                          <span className="text-xs text-gray-400">{order.email}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        ${order.account_size}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <span className="text-sm text-white">{order.platform}</span>
                          <span className="text-xs text-gray-400">{order.server}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status || 'incomplete')}`}>
                          {order.status || 'Incomplete'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                        {formatDate(order.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => {
                            setSelectedOrder(order);
                            setIsViewModalOpen(true);
                          }}
                          className="text-teal-400 hover:text-teal-300 mr-4"
                        >
                          View
                        </button>
                        <button
                          onClick={() => {
                            setSelectedOrder(order);
                            setIsEditModalOpen(true);
                          }}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="px-6 py-4 border-t border-teal-500/20 flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="text-sm text-gray-400">
                Showing {indexOfFirstOrder + 1} to {Math.min(indexOfLastOrder, filteredOrders.length)} of {filteredOrders.length} orders
                {selectedType !== 'all' && (
                  <span className="ml-1 text-teal-400">
                    ({orderTypes.find(t => t.value === selectedType)?.label})
                  </span>
                )}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm rounded-lg bg-gray-800/50 text-gray-400 border border-gray-700/30 hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm rounded-lg bg-gray-800/50 text-gray-400 border border-gray-700/30 hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Modals */}
      <ViewOrderModal
        order={selectedOrder}
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        onEdit={(order) => {
          setIsViewModalOpen(false);
          setSelectedOrder(order);
          setIsEditModalOpen(true);
        }}
      />

      <EditOrderModal
        order={selectedOrder}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onOrderUpdated={(updatedOrder) => {
          handleOrderUpdated(updatedOrder);
          setIsEditModalOpen(false);
        }}
      />
    </div>
  );
}