'use client';

import { useState, useEffect } from 'react';
import { Order, adminService } from '@/services/adminService';
import OrdersTable from '@/components/admin/OrdersTable';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import EditOrderModal from '@/components/admin/EditOrderModal';

export default function RunningOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isPassModalOpen, setIsPassModalOpen] = useState(false);
  const [isFailModalOpen, setIsFailModalOpen] = useState(false);
  const [selectedType, setSelectedType] = useState('all');
  const [loadingOrderId, setLoadingOrderId] = useState<string | number | null>(null);
  const [failReason, setFailReason] = useState('');
  const [selectedFailReason, setSelectedFailReason] = useState('');
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const accountTypeOptions = [
    { label: 'All Types', value: 'all' },
    { label: 'INSTANT', value: 'instant' },
    { label: 'One Step', value: 'onestep' },
    { label: 'Two Step', value: 'twostep' },
    { label: 'Live', value: 'live' }
  ];

  const failReasonOptions = [
    { value: '', label: 'Select a reason' },
    { value: 'drawdown_exceeded', label: 'Drawdown Limit Exceeded' },
    { value: 'profit_target_not_met', label: 'Profit Target Not Met' },
    { value: 'trading_rules_violation', label: 'Trading Rules Violation' },
    { value: 'account_balance_issue', label: 'Account Balance Issue' },
    { value: 'technical_issues', label: 'Technical Issues' },
    { value: 'other', label: 'Other' }
  ];

  // Function to show success toast
  const showSuccessMessage = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessToast(true);
    setTimeout(() => {
      setShowSuccessToast(false);
    }, 3000);
  };

  // Fetch running orders
  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await adminService.getRunningOrders();
        setOrders(data);
      } catch (error) {
        setError('Failed to load orders. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Filter orders based on selected type
  const filteredOrders = selectedType === 'all'
    ? orders
    : orders.filter(order => order.challenge_type?.toLowerCase() === selectedType);

  // Handle order update
  const handleOrderUpdated = (updatedOrder: Order) => {
    setOrders(prevOrders =>
      prevOrders.map(order => order.id === updatedOrder.id ? updatedOrder : order)
    );
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  // Handle pass order
  const handlePassOrder = async (orderId: string | number) => {
    try {
      setLoadingOrderId(orderId);
      const numericOrderId = String(orderId).replace(/^FxE/i, '');

      // First pass the order
      const passedOrder = await adminService.passOrder(numericOrderId, {
        profit_amount: undefined,
        notes: undefined
      });

      // Then edit the passed order with required fields
      const updatedOrder = await adminService.editPassedOrder(numericOrderId, {
        platform_login: passedOrder.platform_login || '',
        platform_password: passedOrder.platform_password || '',
        server: passedOrder.server || '',
        terminal_id: passedOrder.terminal_id || 0,
        session_id: passedOrder.session_id || '',
        profit_target: passedOrder.profit_target || 0,
        account_type: passedOrder.challenge_type?.toLowerCase().includes('twostep') ? 'stage2' : 'live'
      });

      handleOrderUpdated(updatedOrder);
      setIsPassModalOpen(false);
      showSuccessMessage('Order passed successfully!');
    } catch (error) {
      alert('Failed to pass order. Please try again.');
    } finally {
      setLoadingOrderId(null);
    }
  };

  // Handle fail order
  const handleFailOrder = async (orderId: string | number) => {
    if (!selectedFailReason && !failReason) {
      alert('Please select a reason or provide a custom reason');
      return;
    }

    try {
      setLoadingOrderId(orderId);
      const numericOrderId = String(orderId).replace(/^FxE/i, '');

      // Create FormData
      const formData = new FormData();
      formData.append('reason', selectedFailReason === 'other' ? failReason : selectedFailReason);

      // Make the API call to fail the order using directApiCall for URL masking
      const { directApiCall } = await import('@/services/api');
      const updatedOrder = await directApiCall(`order/fail_order/${numericOrderId}`, {
        method: 'POST',
        data: formData
      });

      handleOrderUpdated(updatedOrder);
      setIsFailModalOpen(false);
      setFailReason('');
      setSelectedFailReason('');
      showSuccessMessage('Order failed successfully!');
    } catch (error) {
      alert('Failed to fail order. Please try again.');
    } finally {
      setLoadingOrderId(null);
    }
  };

  return (
    <div className="px-6 pb-6">
      {/* Success Toast */}
      <div
        className={`fixed top-4 right-4 z-50 transform transition-all duration-300 ${
          showSuccessToast ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
        }`}
      >
        <div className="bg-green-500/90 backdrop-blur-sm text-white px-6 py-3 rounded-lg shadow-lg flex items-center space-x-2">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span>{successMessage}</span>
        </div>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Running Orders</h1>
          <p className="text-gray-400">View and manage running orders</p>
        </div>
        <button
          onClick={() => {
            setIsLoading(true);
            setError(null);
            adminService.getRunningOrders()
              .then(data => {
                setOrders(data);
                setIsLoading(false);
              })
              .catch(() => {
                setError('Failed to refresh orders. Please try again.');
                setIsLoading(false);
              });
          }}
          disabled={isLoading}
          className="px-4 py-2 bg-teal-500/20 hover:bg-teal-500/30 rounded-md text-white flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {isLoading ? 'Refreshing...' : 'Refresh Orders'}
        </button>
      </div>

      {/* Account Type Filters */}
      <div className="mb-6 bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-4">
        <h3 className="text-sm font-medium text-gray-400 mb-3">Account Type</h3>
        <div className="flex flex-wrap gap-2">
          {accountTypeOptions.map((type) => (
            <button
              key={type.value}
              onClick={() => setSelectedType(type.value)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedType === type.value
                  ? 'bg-teal-500/20 text-teal-400 border border-teal-500/30'
                  : 'text-gray-400 hover:bg-teal-500/10 hover:text-teal-300'
              }`}
            >
              {type.label}
            </button>
          ))}
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-teal-500">Loading running orders...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400">{error}</p>
          <button
            onClick={() => {
              setIsLoading(true);
              setError(null);
              adminService.getRunningOrders()
                .then(data => {
                  setOrders(data);
                  setIsLoading(false);
                })
                .catch(() => {
                  setError('Failed to load orders. Please try again later.');
                  setIsLoading(false);
                });
            }}
            className="mt-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded-md text-white transition-colors"
          >
            Retry
          </button>
        </div>
      ) : filteredOrders.length === 0 ? (
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-8 text-center">
          <svg className="w-16 h-16 mx-auto text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-xl font-medium text-white mb-2">No Running Orders</h3>
          <p className="text-gray-400 mb-4">There are currently no running orders to display.</p>
        </div>
      ) : (
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-teal-500/20">
              <thead className="bg-[#070F1B]">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Order ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Challenge Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Account Size
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Platform
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-teal-500/20">
                {filteredOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-[#070F1B]/50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                      {order.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      <div>{order.username}</div>
                      <div className="text-xs text-gray-400">{order.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {order.challenge_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      ${order.account_size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {order.platform?.toUpperCase()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs rounded-full bg-teal-500/20 text-teal-400 border border-teal-500/30">
                        Running
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {order.created_at ? new Date(order.created_at).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleViewOrder(order)}
                        className="text-teal-400 hover:text-teal-300"
                      >
                        View
                      </button>
                      <button
                        onClick={() => {
                          setSelectedOrder(order);
                          setIsPassModalOpen(true);
                        }}
                        disabled={loadingOrderId === order.id}
                        className="ml-2 px-3 py-1 rounded-md bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loadingOrderId === order.id ? 'Processing...' : 'Pass'}
                      </button>
                      <button
                        onClick={() => {
                          setSelectedOrder(order);
                          setIsFailModalOpen(true);
                        }}
                        disabled={loadingOrderId === order.id}
                        className="ml-2 px-3 py-1 rounded-md bg-red-500/20 text-red-400 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loadingOrderId === order.id ? 'Processing...' : 'Fail'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* View Order Modal */}
      <ViewOrderModal
        order={selectedOrder}
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
      />

      {/* Pass Confirmation Modal */}
      {selectedOrder && (
        <div className={`fixed inset-0 z-50 overflow-y-auto ${isPassModalOpen ? 'block' : 'hidden'}`}>
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-black opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-[#0F1A2E] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-[#070F1B] px-6 py-4 border-b border-teal-500/20">
                <h3 className="text-lg font-medium text-white">Confirm Pass Order</h3>
              </div>
              <div className="px-6 py-4">
                <p className="text-gray-300">
                  Are you sure you want to pass order #{selectedOrder.id}?
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  This action will mark the order as passed and cannot be undone.
                </p>
                <div className="mt-4 space-y-4">
                  <div>
                    <p className="text-sm text-gray-300">Account Type:</p>
                    <p className="text-sm font-medium text-teal-400">
                      {selectedOrder.challenge_type?.toLowerCase().includes('twostep') ? 'Stage 2' : 'Live'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-300">Server:</p>
                    <p className="text-sm font-medium text-teal-400">{selectedOrder.server || 'Not set'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-300">Platform Login:</p>
                    <p className="text-sm font-medium text-teal-400">{selectedOrder.platform_login || 'Not set'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-300">Terminal ID:</p>
                    <p className="text-sm font-medium text-teal-400">{selectedOrder.terminal_id || 'Not set'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-300">Session ID:</p>
                    <p className="text-sm font-medium text-teal-400">{selectedOrder.session_id || 'Not set'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-300">Profit Target:</p>
                    <p className="text-sm font-medium text-teal-400">${selectedOrder.profit_target || 'Not set'}</p>
                  </div>
                </div>
              </div>
              <div className="bg-[#070F1B] px-6 py-4 border-t border-teal-500/20 flex justify-end space-x-3">
                <button
                  onClick={() => setIsPassModalOpen(false)}
                  className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handlePassOrder(selectedOrder.id)}
                  disabled={loadingOrderId === selectedOrder.id}
                  className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingOrderId === selectedOrder.id ? 'Processing...' : 'Confirm Pass'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fail Confirmation Modal */}
      {selectedOrder && (
        <div className={`fixed inset-0 z-50 overflow-y-auto ${isFailModalOpen ? 'block' : 'hidden'}`}>
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-black opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-[#0F1A2E] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-[#070F1B] px-6 py-4 border-b border-teal-500/20">
                <h3 className="text-lg font-medium text-white">Confirm Fail Order</h3>
              </div>
              <div className="px-6 py-4">
                <p className="text-gray-300">
                  Are you sure you want to fail order #{selectedOrder.id}?
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  This action will mark the order as failed and cannot be undone.
                </p>

                <div className="mt-4">
                  <label htmlFor="failReason" className="block text-sm font-medium text-gray-300 mb-2">
                    Select Reason
                  </label>
                  <select
                    id="failReason"
                    value={selectedFailReason}
                    onChange={(e) => setSelectedFailReason(e.target.value)}
                    className="w-full px-3 py-2 bg-[#070F1B] border border-teal-500/20 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-teal-500/50 focus:border-transparent"
                  >
                    {failReasonOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {selectedFailReason === 'other' && (
                  <div className="mt-4">
                    <label htmlFor="customReason" className="block text-sm font-medium text-gray-300 mb-2">
                      Custom Reason
                    </label>
                    <textarea
                      id="customReason"
                      value={failReason}
                      onChange={(e) => setFailReason(e.target.value)}
                      placeholder="Enter the reason for failing this order..."
                      rows={3}
                      className="w-full px-3 py-2 bg-[#070F1B] border border-teal-500/20 rounded-md text-gray-300 focus:outline-none focus:ring-2 focus:ring-teal-500/50 focus:border-transparent"
                    />
                  </div>
                )}
              </div>
              <div className="bg-[#070F1B] px-6 py-4 border-t border-teal-500/20 flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setIsFailModalOpen(false);
                    setFailReason('');
                    setSelectedFailReason('');
                  }}
                  className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleFailOrder(selectedOrder.id)}
                  disabled={loadingOrderId === selectedOrder.id || (!selectedFailReason && !failReason)}
                  className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingOrderId === selectedOrder.id ? 'Processing...' : 'Confirm Fail'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
