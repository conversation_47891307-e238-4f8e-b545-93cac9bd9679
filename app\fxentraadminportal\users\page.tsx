'use client';

import { useState, useEffect } from 'react';
import { User } from '@/services/adminService';
import UsersTable from '@/components/admin/UsersTable';
import { directApiCall } from '@/services/api';

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all users directly from the API endpoint
  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Direct API call to the provided endpoint
        const data = await directApiCall('auth/users');
        setUsers(Array.isArray(data) ? data : []);
      } catch (error) {
        setError('Failed to load users. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, []);

  return (
    <div className="px-6 pb-6">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Users</h1>
          <p className="text-gray-400">View and manage user accounts</p>
        </div>
        <button
          onClick={() => {
            setIsLoading(true);
            setError(null);
            directApiCall('auth/users')
              .then(data => {
                setUsers(Array.isArray(data) ? data : []);
                setIsLoading(false);
              })
              .catch(() => {
                setError('Failed to refresh users. Please try again.');
                setIsLoading(false);
              });
          }}
          disabled={isLoading}
          className="px-4 py-2 bg-teal-500/20 hover:bg-teal-500/30 rounded-md text-white flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {isLoading ? 'Refreshing...' : 'Refresh Users'}
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-teal-500">Loading users...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded-md text-white transition-colors"
          >
            Retry
          </button>
        </div>
      ) : (
        <UsersTable users={users} />
      )}
    </div>
  );
}
