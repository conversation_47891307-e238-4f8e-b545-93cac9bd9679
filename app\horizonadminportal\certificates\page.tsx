'use client';

import { useState, useEffect } from 'react';
import { Certificate, horizonAdminService } from '@/services/horizonAdminService';

export default function HorizonCertificates() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCertificates = async () => {
      setIsLoading(true);
      try {
        const data = await horizonAdminService.getCertificates();
        setCertificates(data);
      } catch (error) {
        console.error('Error fetching certificates:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCertificates();
  }, []);

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Certificates - Horizon</h1>
        <p className="text-gray-400">View and manage trading certificates</p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-orange-500">Loading certificates...</p>
          </div>
        </div>
      ) : (
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Certificates Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-[#070F1B] border border-orange-500/20 rounded-lg p-4">
              <h4 className="text-orange-400 font-medium">Total Certificates</h4>
              <p className="text-2xl font-bold text-white">{certificates.length}</p>
            </div>
            <div className="bg-[#070F1B] border border-orange-500/20 rounded-lg p-4">
              <h4 className="text-orange-400 font-medium">This Month</h4>
              <p className="text-2xl font-bold text-white">
                {certificates.filter(cert => {
                  const certDate = new Date(cert.issue_date);
                  const now = new Date();
                  return certDate.getMonth() === now.getMonth() && certDate.getFullYear() === now.getFullYear();
                }).length}
              </p>
            </div>
            <div className="bg-[#070F1B] border border-orange-500/20 rounded-lg p-4">
              <h4 className="text-orange-400 font-medium">This Year</h4>
              <p className="text-2xl font-bold text-white">
                {certificates.filter(cert => {
                  const certDate = new Date(cert.issue_date);
                  const now = new Date();
                  return certDate.getFullYear() === now.getFullYear();
                }).length}
              </p>
            </div>
          </div>
          
          {certificates.length > 0 && (
            <div className="mt-6">
              <h4 className="text-lg font-semibold text-white mb-4">Recent Certificates</h4>
              <div className="space-y-3">
                {certificates.slice(0, 5).map((cert, index) => (
                  <div key={index} className="bg-[#070F1B] border border-orange-500/20 rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-white font-medium">{cert.username}</p>
                        <p className="text-gray-400 text-sm">{cert.account_size} - {cert.challenge_type}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-orange-400 text-sm">{cert.issue_date}</p>
                        <p className="text-gray-400 text-xs">#{cert.certificate_number}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 