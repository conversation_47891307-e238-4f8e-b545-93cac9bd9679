'use client';

import { useState, useEffect } from 'react';
import { ContainerAccount, horizonAdminService } from '@/services/horizonAdminService';
import HorizonAddContainerModal from '@/components/admin/HorizonAddContainerModal';

export default function HorizonContainers() {
  const [containers, setContainers] = useState<ContainerAccount[]>([]);
  const [filteredContainers, setFilteredContainers] = useState<ContainerAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Filter states
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [accountSizeFilter, setAccountSizeFilter] = useState<string>('all');
  const [accountTypeFilter, setAccountTypeFilter] = useState<string>('all');

  useEffect(() => {
    const fetchContainers = async () => {
      setIsLoading(true);
      try {
        const data = await horizonAdminService.getContainers();
        setContainers(data);
        setFilteredContainers(data);
      } catch (error) {
        console.error('Error fetching containers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContainers();
  }, []);

  // Filter containers whenever filters change
  useEffect(() => {
    let filtered = containers;

    // Filter by platform
    if (platformFilter !== 'all') {
      filtered = filtered.filter(container => container.platform === platformFilter);
    }

    // Filter by account size
    if (accountSizeFilter !== 'all') {
      filtered = filtered.filter(container => container.account_size === accountSizeFilter);
    }

    // Filter by account type
    if (accountTypeFilter !== 'all') {
      filtered = filtered.filter(container => container.account_type === accountTypeFilter);
    }

    setFilteredContainers(filtered);
  }, [containers, platformFilter, accountSizeFilter, accountTypeFilter]);

  const handleContainerAdded = (newContainer: ContainerAccount) => {
    const updatedContainers = [...containers, newContainer];
    setContainers(updatedContainers);
    setFilteredContainers(updatedContainers);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Containers - Horizon</h1>
          <p className="text-gray-400">Manage trading account containers</p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
        >
          Add Container
        </button>
      </div>

      {/* Filters */}
      <div className="mb-6 bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-4">
        <h3 className="text-lg font-semibold text-white mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Platform Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Platform
            </label>
            <select
              value={platformFilter}
              onChange={(e) => setPlatformFilter(e.target.value)}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
            >
              <option value="all">All Platforms</option>
              <option value="mt4">MT4</option>
              <option value="mt5">MT5</option>
              <option value="ctrader">cTrader</option>
            </select>
          </div>

          {/* Account Size Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Account Size
            </label>
            <select
              value={accountSizeFilter}
              onChange={(e) => setAccountSizeFilter(e.target.value)}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
            >
              <option value="all">All Sizes</option>
              <option value="10000">$10,000</option>
              <option value="25000">$25,000</option>
              <option value="50000">$50,000</option>
              <option value="100000">$100,000</option>
              <option value="200000">$200,000</option>
            </select>
          </div>

          {/* Account Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Account Type
            </label>
            <select
              value={accountTypeFilter}
              onChange={(e) => setAccountTypeFilter(e.target.value)}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
            >
              <option value="all">All Types</option>
              <option value="phase1">Phase 1</option>
              <option value="phase2">Phase 2</option>
              <option value="live">Live</option>
            </select>
          </div>
        </div>

        {/* Filter Summary */}
        <div className="mt-4 flex items-center justify-between">
          <p className="text-sm text-gray-400">
            Showing {filteredContainers.length} of {containers.length} containers
          </p>
          <button
            onClick={() => {
              setPlatformFilter('all');
              setAccountSizeFilter('all');
              setAccountTypeFilter('all');
            }}
            className="text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-orange-500">Loading containers...</p>
          </div>
        </div>
      ) : (
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Container Accounts</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredContainers.map((container) => (
              <div key={container.id} className="bg-[#070F1B] border border-orange-500/20 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="text-white font-medium">{container.platform}</h4>
                    <p className="text-gray-400 text-sm">{container.server}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    container.is_assigned 
                      ? 'bg-red-500/20 text-red-400' 
                      : 'bg-green-500/20 text-green-400'
                  }`}>
                    {container.is_assigned ? 'Assigned' : 'Available'}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Account Size:</span>
                    <span className="text-white">{container.account_size}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Type:</span>
                    <span className="text-white">{container.account_type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span className="text-white">{container.status}</span>
                  </div>
                  {container.order_id && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Order ID:</span>
                      <span className="text-orange-400">{container.order_id}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {containers.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">No containers found</p>
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="mt-4 bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
              >
                Add Your First Container
              </button>
            </div>
          )}

          {containers.length > 0 && filteredContainers.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">No containers match the selected filters</p>
              <button
                onClick={() => {
                  setPlatformFilter('all');
                  setAccountSizeFilter('all');
                  setAccountTypeFilter('all');
                }}
                className="mt-4 bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>
      )}

      <HorizonAddContainerModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onContainerAdded={handleContainerAdded}
      />
    </div>
  );
} 