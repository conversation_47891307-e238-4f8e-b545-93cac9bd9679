'use client';

import { useState, useEffect } from 'react';
import { ContainerAccount, horizonAdminService } from '@/services/horizonAdminService';
import HorizonAddContainerModal from '@/components/admin/HorizonAddContainerModal';

export default function HorizonContainers() {
  const [containers, setContainers] = useState<ContainerAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  useEffect(() => {
    const fetchContainers = async () => {
      setIsLoading(true);
      try {
        const data = await horizonAdminService.getContainers();
        setContainers(data);
      } catch (error) {
        console.error('Error fetching containers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContainers();
  }, []);

  const handleContainerAdded = (newContainer: ContainerAccount) => {
    setContainers(prev => [...prev, newContainer]);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Containers - Horizon</h1>
          <p className="text-gray-400">Manage trading account containers</p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
        >
          Add Container
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-orange-500">Loading containers...</p>
          </div>
        </div>
      ) : (
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-6">Container Accounts</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {containers.map((container) => (
              <div key={container.id} className="bg-[#070F1B] border border-orange-500/20 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="text-white font-medium">{container.platform}</h4>
                    <p className="text-gray-400 text-sm">{container.server}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    container.is_assigned 
                      ? 'bg-red-500/20 text-red-400' 
                      : 'bg-green-500/20 text-green-400'
                  }`}>
                    {container.is_assigned ? 'Assigned' : 'Available'}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Account Size:</span>
                    <span className="text-white">{container.account_size}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Type:</span>
                    <span className="text-white">{container.account_type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span className="text-white">{container.status}</span>
                  </div>
                  {container.order_id && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Order ID:</span>
                      <span className="text-orange-400">{container.order_id}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {containers.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">No containers found</p>
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="mt-4 bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
              >
                Add Your First Container
              </button>
            </div>
          )}
        </div>
      )}

      <HorizonAddContainerModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onContainerAdded={handleContainerAdded}
      />
    </div>
  );
} 