'use client';

import { useState, useEffect } from 'react';
import { Order, horizonAdminService } from '@/services/horizonAdminService';
import OrdersTable from '@/components/admin/OrdersTable';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import EditOrderModal from '@/components/admin/EditOrderModal';

export default function HorizonRunningOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoading(true);
      try {
        const data = await horizonAdminService.getRunningOrders();
        setOrders(data);
      } catch (error) {
        console.error('Error fetching running orders:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const handleOrderUpdated = (updatedOrder: Order) => {
    setOrders(prevOrders =>
      prevOrders.map(order => order.id === updatedOrder.id ? updatedOrder : order)
    );
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsEditModalOpen(true);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Running Orders - Horizon</h1>
        <p className="text-gray-400">View and manage running orders</p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-orange-500">Loading running orders...</p>
          </div>
        </div>
      ) : (
        <>
          <OrdersTable
            orders={orders}
            title="Running Orders"
            onOrderUpdated={handleOrderUpdated}
            onViewOrder={handleViewOrder}
            onEditOrder={handleEditOrder}
          />

          <ViewOrderModal
            order={selectedOrder}
            isOpen={isViewModalOpen}
            onClose={() => setIsViewModalOpen(false)}
            onEdit={handleEditOrder}
          />

          <EditOrderModal
            order={selectedOrder}
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onOrderUpdated={handleOrderUpdated}
          />
        </>
      )}
    </div>
  );
} 