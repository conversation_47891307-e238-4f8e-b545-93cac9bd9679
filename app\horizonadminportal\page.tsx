'use client';

import { useState, useEffect } from 'react';
import { Order, User, AdminDashboardSummary, horizonAdminService } from '@/services/horizonAdminService';
import OrdersTable from '@/components/admin/OrdersTable';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import HorizonEditOrderModal from '@/components/admin/HorizonEditOrderModal';
import DashboardSummary from '@/components/admin/DashboardSummary';
import Link from 'next/link';

export default function HorizonAdminDashboard() {
  const [summary, setSummary] = useState<AdminDashboardSummary | null>(null);
  const [pendingOrders, setPendingOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      try {
        const [summaryData, pendingOrdersData] = await Promise.all([
          horizonAdminService.getDashboardSummary(),
          horizonAdminService.getPendingOrders()
        ]);

        setSummary(summaryData);
        setPendingOrders(pendingOrdersData);
      } catch (error) {
        // Error handling without logging
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Handle order update
  const handleOrderUpdated = (updatedOrder: Order) => {
    // Update the orders list with the updated order
    setPendingOrders(prevOrders =>
      prevOrders.map(order => order.id === updatedOrder.id ? updatedOrder : order)
    );

    // Refresh summary data
    horizonAdminService.getDashboardSummary().then(setSummary);
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsEditModalOpen(true);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Horizon Admin Dashboard</h1>
        <p className="text-gray-400">Manage Horizon trading platform orders and users</p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-orange-500">Loading dashboard data...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Dashboard Summary */}
          {summary && <DashboardSummary summary={summary} />}

          {/* Quick Access Cards */}
          {summary && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Quick Access Cards */}
              <Link
                href="/horizonadminportal/orders/all"
                className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6 hover:border-orange-500/40 transition-colors"
              >
                <h3 className="text-lg font-semibold text-white mb-2">Orders</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-orange-400">{summary.totalOrders}</span>
                  <span className="text-sm text-gray-400">total</span>
                </div>
              </Link>

              <Link
                href="/horizonadminportal/containers"
                className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6 hover:border-orange-500/40 transition-colors"
              >
                <h3 className="text-lg font-semibold text-white mb-2">Containers</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-orange-400">6</span>
                  <span className="text-sm text-gray-400">accounts</span>
                </div>
              </Link>

              <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-white mb-2">Users</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-orange-400">{summary.totalUsers}</span>
                  <span className="text-sm text-gray-400">registered</span>
                </div>
              </div>

              <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-white mb-2">Certificates</h3>
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold text-orange-400">{summary.orderSummary.certificates}</span>
                  <span className="text-sm text-gray-400">issued</span>
                </div>
              </div>
            </div>
          )}

          {/* Recent Orders */}
          <div className="mb-8">
            <OrdersTable
              orders={pendingOrders}
              title="Recent Pending Orders"
              onOrderUpdated={handleOrderUpdated}
              onViewOrder={handleViewOrder}
              onEditOrder={handleEditOrder}
            />
          </div>

          {/* View Order Modal */}
          <ViewOrderModal
            order={selectedOrder}
            isOpen={isViewModalOpen}
            onClose={() => setIsViewModalOpen(false)}
            onEdit={handleEditOrder}
          />

          {/* Edit Order Modal */}
          <HorizonEditOrderModal
            order={selectedOrder}
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onOrderUpdated={handleOrderUpdated}
          />
        </>
      )}
    </div>
  );
} 