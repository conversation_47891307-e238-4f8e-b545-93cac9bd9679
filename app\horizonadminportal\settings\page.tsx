'use client';

import { useState } from 'react';

export default function HorizonSettings() {
  const [settings, setSettings] = useState({
    notifications: true,
    autoRefresh: true,
    darkMode: true,
    language: 'en'
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Settings - Horizon</h1>
        <p className="text-gray-400">Manage your admin portal preferences</p>
      </div>

      <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-orange-500/20 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-6">General Settings</h3>
        
        <div className="space-y-6">
          {/* Notifications */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Email Notifications</h4>
              <p className="text-gray-400 text-sm">Receive notifications for new orders and updates</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications}
                onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
            </label>
          </div>

          {/* Auto Refresh */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Auto Refresh</h4>
              <p className="text-gray-400 text-sm">Automatically refresh data every 30 seconds</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.autoRefresh}
                onChange={(e) => handleSettingChange('autoRefresh', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
            </label>
          </div>

          {/* Dark Mode */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Dark Mode</h4>
              <p className="text-gray-400 text-sm">Use dark theme for better visibility</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.darkMode}
                onChange={(e) => handleSettingChange('darkMode', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
            </label>
          </div>

          {/* Language */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Language</h4>
              <p className="text-gray-400 text-sm">Select your preferred language</p>
            </div>
            <select
              value={settings.language}
              onChange={(e) => handleSettingChange('language', e.target.value)}
              className="bg-[#070F1B] border border-orange-500/20 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-orange-500"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </select>
          </div>
        </div>

        {/* Save Button */}
        <div className="mt-8 pt-6 border-t border-orange-500/20">
          <button className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md font-medium transition-colors">
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
} 