'use client';

import { useState, useEffect } from 'react';
import { User, horizonAdminService } from '@/services/horizonAdminService';
import UsersTable from '@/components/admin/UsersTable';

export default function HorizonUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true);
      try {
        const data = await horizonAdminService.getAllUsers();
        setUsers(data);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, []);

  return (
    <div className="px-6 pb-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Users - Horizon</h1>
        <p className="text-gray-400">View and manage registered users</p>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-orange-500">Loading users...</p>
          </div>
        </div>
      ) : (
        <UsersTable users={users} />
      )}
    </div>
  );
} 