import './globals.css'
import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import LayoutWrapper from '../components/LayoutWrapper'
import MetaPixel from '../components/MetaPixel'

const inter = Inter({ subsets: ['latin'] })

// Separate viewport configuration
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
}

export const metadata: Metadata = {
  metadataBase: new URL('https://fxentra.com'),
  title: 'FXentra - Professional Trading Platform',
  description: 'Trade forex, crypto, and other markets with up to $200,000 in capital. Professional trading platform with transparent rules and fast payouts.',
  keywords: 'prop firm, professional trading, forex, crypto, stocks, trading platform, proprietary trading, trading evaluation',
  authors: [{ name: 'FXentra Team' }],
  category: 'Finance',
  icons: {
    icon: [
      { url: '/images/favicon.ico' },
      { url: '/images/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
    ],
    apple: [
      { url: '/images/favicon.ico', sizes: '180x180', type: 'image/x-icon' },
    ],
  },
  openGraph: {
    type: 'website',
    title: 'FXentra - Professional Trading Platform',
    description: 'Trade with up to $200,000 in capital. No hidden fees, transparent rules, and fast payouts.',
    url: 'https://fxentra.com',
    siteName: 'FXentra',
    images: [
      {  

        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'FXentra - Professional Trading Platform',
      },
    ],
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FXentra - Professional Trading Platform',
    description: 'Trade forex, crypto, and other markets with up to $200,000 in capital. Professional trading platform with transparent rules.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <MetaPixel />
      </head>
      <body className={`${inter.className} bg-dark`}>
        <div className="relative min-h-screen overflow-hidden">
          {/* Background decorative elements */}
          <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-dark to-dark opacity-90"></div>
            
            {/* Grid pattern */}
            <div 
              className="absolute inset-0 opacity-[0.03]" 
              style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                backgroundSize: '30px 30px'
              }}
            ></div>
            
            {/* Geometric shapes */}
            <div className="geometric-shape shape-teal-glow w-[800px] h-[800px] -top-[400px] -left-[400px]"></div>
            <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] -bottom-[200px] -right-[200px]"></div>
            <div className="geometric-shape shape-teal-glow w-[500px] h-[500px] top-[30%] -right-[250px] opacity-[0.07]"></div>
          </div>
          
          {/* Content */}
          <LayoutWrapper>{children}</LayoutWrapper>
        </div>
      </body>
    </html>
  );
} 
