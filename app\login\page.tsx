'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { authService } from '@/services/api';
import toast, { Toaster } from 'react-hot-toast';

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [animateForm, setAnimateForm] = useState(false);
  const [redirectPath, setRedirectPath] = useState('/dashboard');

  useEffect(() => {
    // Start animations after component mounts
    setAnimateForm(true);

    // Check URL parameters for success messages and redirect path
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('verified') === 'true') {
      setSuccessMessage('Email verified successfully! You can now log in.');
    } else if (urlParams.get('registered') === 'true') {
      setSuccessMessage('Account created successfully! Please check your email for verification.');
    }

    // Get redirect path from URL if present
    const redirect = urlParams.get('redirect');
    if (redirect) {
      setRedirectPath(redirect);
    }

    // If user is already authenticated, redirect
    if (authService.isAuthenticated()) {
      router.replace(redirectPath);
    }
  }, [router, redirectPath]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Call the login API
      await authService.login(formData.username, formData.password);

      // Show success toast
      toast.success('Login successful! Redirecting...', {
        style: {
          background: 'rgba(0, 0, 0, 0.9)',
          color: '#fff',
          border: '1px solid rgba(20, 184, 166, 0.3)',
        },
        icon: '🚀',
        duration: 3000,
      });

      // Ensure token is set before redirecting
      if (authService.isAuthenticated()) {
        router.replace(redirectPath);
      } else {
        throw new Error('Authentication failed. Please try again.');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || 'Invalid username or password. Please try again.';
      setError(errorMessage);

      // Show error toast
      toast.error(errorMessage, {
        style: {
          background: 'rgba(0, 0, 0, 0.9)',
          color: '#ff4e4e',
          border: '1px solid rgba(255, 78, 78, 0.3)',
        },
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
      {/* Toast Container */}
      <Toaster position="top-right" />

      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900 to-teal-900/50 z-0">
        {/* Animated grid background */}
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: 'linear-gradient(to right, rgba(0, 255, 209, 0.15) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 255, 209, 0.15) 1px, transparent 1px)',
          backgroundSize: '40px 40px'
        }}></div>

        {/* Background glow effects */}
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-teal-400/10 rounded-full blur-[120px] animate-pulse-slow"></div>
        <div className="absolute bottom-0 right-1/4 w-[500px] h-[500px] bg-teal-500/5 rounded-full blur-[120px] animate-pulse-slow-delay"></div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-teal-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute top-40 right-40 w-3 h-3 bg-teal-500 rounded-full animate-float-delay opacity-60"></div>
        <div className="absolute bottom-40 left-1/3 w-2 h-2 bg-teal-400 rounded-full animate-float-reverse opacity-40"></div>
        <div className="absolute bottom-20 right-1/3 w-2 h-2 bg-teal-300 rounded-full animate-float-delay-reverse opacity-70"></div>
      </div>

      {/* Login Form Content */}
      <div className="relative py-14 px-4 sm:px-6 lg:px-8 z-10 flex justify-center">
        <div className="max-w-sm w-full mx-auto bg-gray-900/30 backdrop-blur-xl rounded-3xl border border-teal-500/20 p-6 md:p-8 shadow-2xl">
          {/* Card glow effect */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-teal-400/5 to-teal-600/5"></div>

          {/* Logo */}
          <div className="mb-6 text-center">
            <div className="flex justify-center mb-4">
              <Image
                src="/images/fxentra-logo.png"
                alt="FXentra Logo"
                width={150}
                height={55}
                className="object-contain"
              />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent mb-3">Sign In</h2>
            <div className="w-20 h-1 bg-gradient-to-r from-teal-300 to-teal-500 rounded-full mx-auto"></div>
            <p className="text-gray-400 mt-4 text-sm">
              Welcome back, please login to your account
            </p>
          </div>

        {successMessage && (
            <div className="mb-8 px-5 py-4 rounded-lg bg-green-900/20 border border-green-600/30 text-green-400 text-sm animate-fade-in">
              <div className="flex items-center">
                <svg className="h-5 w-5 mr-3 text-green-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                {successMessage}
              </div>
            </div>
        )}

        {error && (
            <div className="mb-8 px-5 py-4 rounded-lg bg-red-900/20 border border-red-600/30 text-red-400 text-sm animate-fade-in">
              <div className="flex items-center">
                <svg className="h-5 w-5 mr-3 text-red-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {error}
              </div>
            </div>
        )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Email Field */}
            <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-3">
                  Email
                </label>
                <div className="relative group">
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required
                placeholder="Enter your Email"
                className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                value={formData.username}
                onChange={handleChange}
              />
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

              {/* Password Field */}
            <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-3">
                  Password
                </label>
                <div className="relative group">
              <input
                id="password"
                name="password"
                    type={showPassword ? "text" : "password"}
                autoComplete="current-password"
                required
                placeholder="Password"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50 pr-12"
                value={formData.password}
                onChange={handleChange}
              />
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-400 focus:outline-none transition-colors z-10"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
                {error && error.includes('password') && (
                  <p className="mt-1 text-sm text-red-400">Password is required</p>
                )}
            </div>
          </div>

            <div className="flex items-center justify-between mt-8">
            <div className="flex items-center">
                <div className="relative">
              <input
                id="remember_me"
                name="remember_me"
                type="checkbox"
                    className="h-5 w-5 appearance-none bg-gray-800/70 border-2 border-teal-500/40 rounded text-teal-500 focus:ring-teal-500/30 focus:border-teal-500 transition-colors duration-200 cursor-pointer checked:bg-teal-500 checked:border-transparent"
                    checked={rememberMe}
                    onChange={() => setRememberMe(!rememberMe)}
                  />
                  <div className={`absolute inset-0 bg-teal-500/10 rounded pointer-events-none transition-opacity duration-300 ${rememberMe ? 'opacity-100' : 'opacity-0'}`}></div>
                  {rememberMe && (
                    <svg className="absolute inset-0 h-5 w-5 text-white pointer-events-none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <label htmlFor="remember_me" className="ml-3 block text-sm text-gray-300">
                Remember me
              </label>
            </div>

            <div className="text-sm">
                <Link href="/forgot-password" className="text-teal-400 hover:text-teal-300 font-medium transition-colors relative z-10">
                  Forgot Password?
              </Link>
            </div>
          </div>

            {/* Submit button with animations */}
            <div className="mt-8">
            <button
              type="submit"
              disabled={isLoading}
                className="relative w-full flex items-center justify-center py-3 px-6 rounded-xl text-white group overflow-hidden"
              >
                {/* Button background with glow */}
                <div className="absolute inset-0 bg-gradient-to-r from-teal-500 to-teal-400 rounded-xl"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-500 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Button shine effect */}
                <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-all duration-700 opacity-30 bg-gradient-to-r from-transparent via-white to-transparent rounded-xl"></div>

                {/* Button content */}
                <span className="relative flex items-center">
              {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                      <span>Signing in...</span>
                    </>
                  ) : (
                    <span className="font-medium">Sign in</span>
                  )}
                </span>
            </button>
          </div>
        </form>

          <div className="mt-10 text-center text-sm text-gray-400 relative z-10">
            Don't have an account?{' '}
            <Link href="/signup" className="text-teal-400 hover:text-teal-300 font-medium transition-colors">
              Sign up
            </Link>
          </div>
        </div>
      </div>

      {/* Custom animations and styles */}
      <style jsx global>{`
        @keyframes pulse-very-slow {
          0%, 100% { opacity: 0; }
          50% { opacity: 0.3; }
        }

        @keyframes pulse-slow {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.5; }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
        }

        @keyframes float-delay {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
          animation-delay: 1s;
        }

        @keyframes float-reverse {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(15px); }
        }

        @keyframes float-delay-reverse {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(15px); }
          animation-delay: 1.5s;
        }

        .animate-pulse-very-slow {
          animation: pulse-very-slow 10s ease-in-out infinite;
        }

        .animate-pulse-slow {
          animation: pulse-slow 6s ease-in-out infinite;
        }

        .animate-pulse-slow-delay {
          animation: pulse-slow 6s ease-in-out infinite;
          animation-delay: 3s;
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .animate-float-delay {
          animation: float-delay 7s ease-in-out infinite;
          animation-delay: 2s;
        }

        .animate-float-reverse {
          animation: float-reverse 8s ease-in-out infinite;
        }

        .animate-float-delay-reverse {
          animation: float-delay-reverse 9s ease-in-out infinite;
          animation-delay: 3s;
        }

        .animate-fade-in {
          animation: fadeIn 0.4s ease-out forwards;
        }

        @keyframes fadeIn {
          0% { opacity: 0; transform: translateY(-5px); }
          100% { opacity: 1; transform: translateY(0); }
        }
      `}</style>
    </div>
  );
}