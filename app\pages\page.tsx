"use client"
import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Container, TextField, Typography, Paper } from '@mui/material';
import { useRouter } from 'next/navigation';

const VerifyEmail = () => {
  const [verificationCode, setVerificationCode] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    // Get email from localStorage
    const storedEmail = localStorage.getItem('verification-email');
    if (storedEmail) {
      setEmail(storedEmail);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const { directApiCall } = await import('@/services/api');
      const response = await directApiCall(`auth/verify-email?email=${encodeURIComponent(email)}&code=${verificationCode}`, {
        method: 'POST'
      });

      if (response) {
        // Handle successful verification
        localStorage.removeItem('verification-email'); // Clean up
        router.push('/login'); // Redirect to login page
      }
    } catch (err) {
      setError('Verification failed. Please check your code and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Paper elevation={3} sx={{ p: 4, width: '100%' }}>
          <Typography component="h1" variant="h5" align="center" gutterBottom>
            Email Verification
          </Typography>

          {email && (
            <Typography variant="body1" align="center" sx={{ mb: 3 }}>
              Please enter the verification code sent to {email}
            </Typography>
          )}

          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Verification Code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              margin="normal"
              required
              autoFocus
            />

            {error && (
              <Typography color="error" variant="body2" sx={{ mt: 2 }}>
                {error}
              </Typography>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? 'Verifying...' : 'Verify Email'}
            </Button>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default VerifyEmail;