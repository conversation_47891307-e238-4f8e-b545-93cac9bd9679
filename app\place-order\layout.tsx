'use client';

import { Inter } from 'next/font/google';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export default function PlaceOrderLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className={`${inter.className} min-h-screen bg-[#070F1B]`}>
      <div className="relative min-h-screen">
        {/* Background elements */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          {/* Gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#070F1B] via-[#0F1A2E] to-[#0A1A2F] opacity-80"></div>
          
          {/* Grid overlay */}
          <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-repeat opacity-[0.02]"></div>
          
          {/* Geometric shapes */}
          <div className="geometric-shape shape-teal-glow w-[800px] h-[800px] -top-[400px] -left-[400px] opacity-[0.15]"></div>
          <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] -bottom-[200px] -right-[200px] opacity-[0.1]"></div>
          <div className="geometric-shape shape-teal-glow w-[500px] h-[500px] top-[30%] -right-[250px] opacity-[0.07]"></div>
        </div>
            
        {/* Main content area */}
        <div className="relative z-10">
          {children}
        </div>
      </div>
    </div>
  );
}
