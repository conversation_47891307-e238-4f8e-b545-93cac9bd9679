'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/api';
import { placeOrder } from '@/services/orderService';

// Challenge types and account sizes
const challengeTypes = ['INSTANT', 'Standard', 'Elite Trader', 'Pro Trader'];
const accountSizes = ['5000', '10000', '25000', '50000', '100000', '200000'];
const platforms = ['mt4', 'mt5'];
const paymentMethods = ['USDT', 'Bitcoin', 'Credit Card', 'Bank Transfer'];

export default function PlaceOrderPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form data
  const [formData, setFormData] = useState({
    email: '',
    challenge_type: challengeTypes[0],
    account_size: accountSizes[0],
    platform: platforms[0],
    payment_method: paymentMethods[0],
    txid: '',
  });

  // File state
  const [image, setImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showFallbackImage, setShowFallbackImage] = useState(false);

  // Get user email if authenticated
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      router.push('/login');
    } else {
      // Set email from authenticated user if available
      const username = authService.getUsername();
      if (username) {
        setFormData(prev => ({ ...prev, email: username }));
      }
    }
  }, [router]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle file input changes
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setError('Image size should be less than 10MB');
        return;
      }

      // Check file type
      if (!file.type.match(/image\/(jpeg|png|gif)/)) {
        setError('Please upload a valid image file (JPEG, PNG, or GIF)');
        return;
      }

      setImage(file);
      setError(''); // Clear any previous errors
      setShowFallbackImage(false); // Reset fallback image state

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        setPreviewUrl(result);

        // Verify the preview URL is valid
        if (!result.startsWith('data:image/')) {
          setError('Failed to create image preview');
          setPreviewUrl(null);
          setShowFallbackImage(true);
        }
      };
      reader.onerror = () => {
        setError('Failed to load image preview');
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!image) {
      setError('Please upload a payment proof image');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Create form data for submission
      const orderFormData = new FormData();
      orderFormData.append('email', formData.email);
      orderFormData.append('challenge_type', formData.challenge_type);
      orderFormData.append('account_size', formData.account_size);
      orderFormData.append('platform', formData.platform);
      orderFormData.append('payment_method', formData.payment_method);
      orderFormData.append('txid', formData.txid);
      orderFormData.append('image', image);

      // Submit order
      await placeOrder(orderFormData);

      // Show success message
      setSuccess('Order placed successfully! Redirecting to home page...');

      // Reset form
      setFormData({
        email: formData.email, // Keep email
        challenge_type: challengeTypes[0],
        account_size: accountSizes[0],
        platform: platforms[0],
        payment_method: paymentMethods[0],
        txid: '',
      });
      setImage(null);
      setPreviewUrl(null);
      setShowFallbackImage(false);

      // Redirect to home page after 3 seconds
      setTimeout(() => {
        window.location.href = '/';
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to place order. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#070F1B] px-4 py-12">
      <div className="relative z-10 w-full max-w-2xl">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="geometric-shape shape-teal-glow w-[400px] h-[400px] -top-[200px] -left-[200px] opacity-[0.15]"></div>
          <div className="geometric-shape shape-teal-glow w-[300px] h-[300px] -bottom-[150px] -right-[150px] opacity-[0.1]"></div>
        </div>

        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-8 shadow-xl">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-white">Place Your Order</h1>
            <p className="text-gray-400 mt-2">Fill in the details to place your trading challenge order</p>
          </div>

          {error && (
            <div className="bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-500/10 border border-green-500/20 text-green-400 px-4 py-3 rounded-lg mb-6">
              {success}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                Email <span className="text-teal-500">*</span>
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-transparent"
                placeholder="Enter your email"
              />
            </div>

            {/* Challenge Type and Account Size - Two columns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Challenge Type */}
              <div>
                <label htmlFor="challenge_type" className="block text-sm font-medium text-gray-300 mb-2">
                  Challenge Type <span className="text-teal-500">*</span>
                </label>
                <select
                  id="challenge_type"
                  name="challenge_type"
                  required
                  value={formData.challenge_type}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-transparent"
                >
                  {challengeTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              {/* Account Size */}
              <div>
                <label htmlFor="account_size" className="block text-sm font-medium text-gray-300 mb-2">
                  Account Size <span className="text-teal-500">*</span>
                </label>
                <select
                  id="account_size"
                  name="account_size"
                  required
                  value={formData.account_size}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-transparent"
                >
                  {accountSizes.map((size) => (
                    <option key={size} value={size}>
                      ${size}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Platform and Payment Method - Two columns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Platform */}
              <div>
                <label htmlFor="platform" className="block text-sm font-medium text-gray-300 mb-2">
                  Platform <span className="text-teal-500">*</span>
                </label>
                <select
                  id="platform"
                  name="platform"
                  required
                  value={formData.platform}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-transparent"
                >
                  {platforms.map((platform) => (
                    <option key={platform} value={platform}>
                      {platform.toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              {/* Payment Method */}
              <div>
                <label htmlFor="payment_method" className="block text-sm font-medium text-gray-300 mb-2">
                  Payment Method <span className="text-teal-500">*</span>
                </label>
                <select
                  id="payment_method"
                  name="payment_method"
                  required
                  value={formData.payment_method}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-transparent"
                >
                  {paymentMethods.map((method) => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Transaction ID */}
            <div>
              <label htmlFor="txid" className="block text-sm font-medium text-gray-300 mb-2">
                Transaction ID <span className="text-teal-500">*</span>
              </label>
              <input
                id="txid"
                name="txid"
                type="text"
                required
                value={formData.txid}
                onChange={handleChange}
                className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500 focus:border-transparent"
                placeholder="Enter transaction ID"
              />
            </div>

            {/* Image Upload */}
            <div>
              <label htmlFor="image" className="block text-sm font-medium text-gray-300 mb-2">
                Payment Proof Image <span className="text-teal-500">*</span>
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-teal-500/20 border-dashed rounded-lg bg-[#070F1B]/50">
                <div className="space-y-1 text-center">
                  {previewUrl ? (
                    <div className="flex flex-col items-center">
                      <div className="relative group w-full">
                        <div className="bg-gradient-to-r from-teal-500/20 to-blue-500/20 p-1 rounded-lg">
                          {showFallbackImage ? (
                            <div className="relative h-64 w-full flex flex-col items-center justify-center bg-black/20 rounded-md border border-teal-500/30 mb-2">
                              <svg className="w-16 h-16 text-teal-500/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <p className="text-teal-400 mt-4">Image preview not available</p>
                              <p className="text-gray-400 text-sm mt-1">The file will still be uploaded</p>
                            </div>
                          ) : (
                            <div className="relative">
                              <img
                                src={previewUrl}
                                alt="Payment Proof"
                                className="h-64 w-full object-contain mb-2 rounded-md border border-teal-500/30 bg-black/20"
                                onError={() => {
                                  setError('Failed to load image preview');
                                  setShowFallbackImage(true);
                                }}
                              />
                              <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                                Preview
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-md flex items-center justify-center">
                          <button
                            type="button"
                            onClick={() => {
                              setImage(null);
                              setPreviewUrl(null);
                              setShowFallbackImage(false);
                            }}
                            className="text-sm text-red-400 hover:text-red-300 bg-black/70 px-4 py-2 rounded-lg border border-red-500/30 hover:border-red-500/50 transition-all duration-200"
                          >
                            Remove Image
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mt-2 bg-[#070F1B] px-3 py-1.5 rounded-md border border-teal-500/20">
                        <svg className="w-4 h-4 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <div className="text-sm text-gray-300">
                          {image?.name} <span className="text-xs text-teal-500/80">({(image?.size || 0) / 1024 / 1024 > 1
                            ? `${((image?.size || 0) / 1024 / 1024).toFixed(2)} MB`
                            : `${((image?.size || 0) / 1024).toFixed(2)} KB`})</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <>
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex text-sm text-gray-400">
                        <label
                          htmlFor="file-upload"
                          className="relative cursor-pointer rounded-md font-medium text-teal-400 hover:text-teal-300 focus-within:outline-none"
                        >
                          <span>Upload a file</span>
                          <input
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            className="sr-only"
                            accept="image/jpeg,image/png,image/gif"
                            onChange={handleFileChange}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 ${
                isLoading ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                'Place Order'
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
