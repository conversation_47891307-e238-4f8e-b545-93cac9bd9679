'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface PortalOption {
  id: string;
  name: string;
  description: string;
  color: string;
  bgColor: string;
  borderColor: string;
  hoverColor: string;
  logo: string;
  route: string;
}

export default function PortalsPage() {
  const [selectedPortal, setSelectedPortal] = useState<string | null>(null);

  const portals: PortalOption[] = [
    {
      id: 'fxentra',
      name: 'FXentra Admin',
      description: 'Manage FXentra trading platform orders, users, and containers',
      color: 'text-teal-400',
      bgColor: 'bg-teal-500/10',
      borderColor: 'border-teal-500/20',
      hoverColor: 'hover:border-teal-500/40',
      logo: '/images/fxentra-logo.png',
      route: '/fxentraadminportal'
    },
    {
      id: 'horizon',
      name: 'Horizon Admin',
      description: 'Manage Horizon trading platform orders, users, and containers',
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      borderColor: 'border-orange-500/20',
      hoverColor: 'hover:border-orange-500/40',
      logo: '/images/fxentra-logo.png', // Using same logo for now
      route: '/horizonadminportal'
    },
    {
      id: 'whales',
      name: 'Whales Admin',
      description: 'Manage Whales trading platform orders, users, and containers',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/20',
      hoverColor: 'hover:border-blue-500/40',
      logo: '/images/fxentra-logo.png', // Using same logo for now
      route: '/whalesadminportal'
    }
  ];

  return (
    <div className="min-h-screen bg-[#0B1221] flex items-center justify-center p-6">
      {/* Background decorative elements */}
      <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-dark to-dark opacity-90"></div>
        
        {/* Grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '30px 30px'
          }}
        />

        {/* Geometric shapes */}
        <div className="geometric-shape shape-teal-glow w-[800px] h-[800px] -top-[400px] -left-[400px]"></div>
        <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] -bottom-[200px] -right-[200px]"></div>
        <div className="geometric-shape shape-teal-glow w-[500px] h-[500px] top-[30%] -right-[250px] opacity-[0.07]"></div>
      </div>

      <div className="relative z-10 w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Admin Portals</h1>
          <p className="text-gray-400 text-lg">Select an admin portal to manage</p>
        </div>

        {/* Portal Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {portals.map((portal) => (
            <Link
              key={portal.id}
              href={portal.route}
              className={`group relative bg-[#0F1A2E]/90 backdrop-blur-xl border ${portal.borderColor} rounded-2xl p-8 transition-all duration-300 ${portal.hoverColor} hover:scale-105 hover:shadow-2xl`}
              onMouseEnter={() => setSelectedPortal(portal.id)}
              onMouseLeave={() => setSelectedPortal(null)}
            >
              {/* Portal Icon */}
              <div className="flex justify-center mb-6">
                <div className={`w-20 h-20 ${portal.bgColor} rounded-full flex items-center justify-center`}>
                  <Image
                    src={portal.logo}
                    alt={`${portal.name} Logo`}
                    width={40}
                    height={40}
                    className="object-contain"
                  />
                </div>
              </div>

              {/* Portal Info */}
              <div className="text-center">
                <h3 className={`text-xl font-bold ${portal.color} mb-3`}>
                  {portal.name}
                </h3>
                <p className="text-gray-400 text-sm leading-relaxed">
                  {portal.description}
                </p>
              </div>

              {/* Hover Effect */}
              <div className={`absolute inset-0 rounded-2xl ${portal.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
              
              {/* Arrow Icon */}
              <div className={`absolute top-4 right-4 ${portal.color} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </div>
            </Link>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-12">
          <p className="text-gray-500 text-sm">
            All portals provide the same administrative features with platform-specific data
          </p>
        </div>
      </div>
    </div>
  );
} 