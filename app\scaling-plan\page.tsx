'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

const requirements = [
  'Minimum of 4 months cycle',
  'At least 10% total net simulated profit in the 4-months trading window, date to date',
  'Process at least 2 rewards',
  'Positive account balance at the time of scale-up',
];

const benefits = [
  'Upgraded reward to 90%',
  '$2M scale-up cap',
  'Continuous increase when criteria are met',
];

const benefitIcons = [
  (
    <svg key="1" className="w-7 h-7 text-teal-400" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M12 8c1.11 0 2.08.402 2.599 1M12 8V7m0 1c1.657 0 3 .895 3 2s-1.343 2-3 2-3 .895-3 2 1.343 2 3 2" /></svg>
  ),
  (
    <svg key="2" className="w-7 h-7 text-blue-400" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /></svg>
  ),
  (
    <svg key="3" className="w-7 h-7 text-purple-400" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
  ),
];

export default function ScalingPlanPage() {
  const [selectedCurrency, setSelectedCurrency] = useState('usd');

  const currencies = [
    { code: 'usd', symbol: '$', name: 'USD' },
    { code: 'gbp', symbol: '£', name: 'GBP' },
    { code: 'eur', symbol: '€', name: 'EUR' },
    { code: 'czk', symbol: 'Kč', name: 'CZK' },
    { code: 'cad', symbol: 'C$', name: 'CAD' },
    { code: 'aud', symbol: 'A$', name: 'AUD' },
    { code: 'chf', symbol: 'CHF', name: 'CHF' }
  ];

  const scalingData = [
    { elapsedTime: '0 months', initialBalance: 400000, maxDailyLoss: 20000, maxLoss: 40000 },
    { elapsedTime: '4 months', initialBalance: 500000, maxDailyLoss: 25000, maxLoss: 50000 },
    { elapsedTime: '8 months', initialBalance: 600000, maxDailyLoss: 30000, maxLoss: 60000 },
    { elapsedTime: '12 months', initialBalance: 700000, maxDailyLoss: 35000, maxLoss: 70000 },
    { elapsedTime: '16 months', initialBalance: 800000, maxDailyLoss: 40000, maxLoss: 80000 }
  ];

  const formatCurrency = (amount: number) => {
    const currency = currencies.find(c => c.code === selectedCurrency);
    return `${currency?.symbol}${amount.toLocaleString()}`;
  };

  return (
    <div className="min-h-screen bg-[#030609] relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200 overflow-x-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/10 via-teal-400/10 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/10 via-teal-400/10 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
        <div className="absolute left-1/2 top-0 w-1 h-40 bg-gradient-to-b from-teal-400/30 to-transparent rounded-full blur-xl animate-fadeIn"></div>
      </div>

      {/* Hero Section */}
      <div className="relative z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-blue-500/10 to-teal-500/10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#030609]/20 to-[#030609]/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center relative">
            <span className="inline-block px-5 py-2 mb-6 rounded-full bg-gradient-to-r from-teal-500/20 to-blue-500/20 border border-teal-500/30 text-teal-300 text-sm font-semibold tracking-wider shadow-md backdrop-blur-md animate-fadeIn">SCALING PLAN</span>
            <h1 className="text-5xl md:text-7xl font-extrabold text-white mb-6 leading-tight drop-shadow-xl animate-fadeIn">
              <span className="bg-gradient-to-r from-white via-teal-100 to-white bg-clip-text text-transparent">
                Reward Growth
              </span>
              <br />
              <span className="bg-gradient-to-r from-teal-400 via-blue-400 to-teal-400 bg-clip-text text-transparent">
                & Scaling Plan
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed font-light animate-fadeIn">
              Maximise your reward with FXentra's scaling plan and earn up to <span className="text-teal-400 font-bold">90%</span> of your simulated profits
            </p>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto mt-6 leading-relaxed animate-fadeIn">
              Discover the size of the reward available for each FXentra Trader and learn how to maximise it. FXentra offers opportunities to elevate your reward up to 90% through an incentive scaling plan.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
        {/* Reward Growth Section */}
        <div className="mb-20">
          <div className="bg-[#0F1A2A]/80 rounded-3xl border border-teal-900/30 shadow-2xl p-8 md:p-14 backdrop-blur-xl flex flex-col md:flex-row items-center gap-10 animate-fadeIn">
            <div className="flex-1 flex flex-col justify-center items-center md:items-start">
              <div className="flex items-center gap-4 mb-4">
                <span className="inline-flex items-center justify-center w-14 h-14 rounded-full bg-gradient-to-br from-teal-500/80 to-blue-500/80 shadow-lg">
                  <span className="text-3xl font-extrabold text-white">80%</span>
                </span>
                <span className="text-lg font-semibold text-teal-300">Base Reward</span>
              </div>
              <p className="text-lg text-gray-300 leading-relaxed mb-4">
                Each trader with an FXentra Account is entitled to receive a reward equivalent to <span className="text-teal-400 font-semibold">80%</span> of simulated profits. This means you are entitled to the reward after meeting the necessary conditions and achieving success on an FXentra Account.
              </p>
              <ul className="text-gray-400 text-base space-y-2 mb-4">
                <li><span className="font-semibold text-blue-400">Bi-weekly</span> reward processing</li>
                <li><span className="font-semibold text-teal-400">8 hours</span> average payout time</li>
              </ul>
            </div>
            <div className="flex-1 flex flex-col items-center">
              <div className="relative w-full max-w-md rounded-2xl overflow-hidden shadow-xl border border-teal-900/30 bg-[#101C2C]/80">
                <Image
                  src="https://ftmo.com/wp-content/uploads/2024/04/payout-ratio.webp"
                  alt="FXentra Payout Ratio - 80% Reward"
                  width={600}
                  height={300}
                  className="object-contain w-full h-auto"
                  priority
                />
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/60 text-xs text-gray-200 px-3 py-1 rounded-full shadow-md mt-2">Payout Ratio Example</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scaling Plan Section */}
        <div className="mb-20">
          <div className="bg-[#0F1A2A]/80 rounded-3xl border border-blue-900/30 shadow-2xl p-8 md:p-14 backdrop-blur-xl animate-fadeIn">
            <div className="flex flex-col md:flex-row gap-10 items-center">
              <div className="flex-1 flex flex-col gap-6">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-2">Scaling Plan</h2>
                <p className="text-lg text-gray-300 leading-relaxed mb-4">
                  Talented FXentra Traders who meet our Scaling Plan requirements are rewarded with a <span className="text-teal-400 font-semibold">25% increase</span> in their FXentra Account balance and a bigger reward of <span className="text-blue-400 font-semibold">90%</span>. FXentra Accounts can be increased to a maximum initial balance of <span className="text-teal-400 font-semibold">$2,000,000</span>!
                </p>
                {/* Timeline/Stepper for Requirements */}
                <div className="mb-6">
                  <h3 className="text-lg font-bold text-teal-300 mb-2">Requirements</h3>
                  <ol className="relative border-l-2 border-teal-700/40 pl-6 space-y-4">
                    {requirements.map((req, i) => (
                      <li key={i} className="flex items-start gap-3 group">
                        <span className="w-4 h-4 mt-1 rounded-full bg-gradient-to-br from-teal-400 to-blue-400 border-2 border-white shadow-md group-hover:scale-110 transition-transform"></span>
                        <span className="text-gray-200 group-hover:text-teal-200 transition-colors">{req}</span>
                      </li>
                    ))}
                  </ol>
                </div>
                {/* Feature Grid for Benefits */}
                <div>
                  <h3 className="text-lg font-bold text-blue-300 mb-2">Benefits</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {benefits.map((b, i) => (
                      <div key={i} className="flex flex-col items-center bg-[#101C2C]/80 rounded-xl p-4 border border-blue-900/30 shadow hover:shadow-lg transition-shadow">
                        {benefitIcons[i]}
                        <span className="mt-2 text-base text-gray-200 font-semibold text-center">{b}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="flex-1 flex flex-col items-center">
                <div className="relative w-full max-w-xl rounded-2xl overflow-hidden shadow-xl border border-blue-900/30 bg-[#101C2C]/80">
                  <Image
                    src="https://ftmo.com/wp-content/uploads/2024/05/Frame-13613.png"
                    alt="FXentra Scaling Plan - Up to $2,000,000 Account Balance"
                    width={800}
                    height={400}
                    className="object-contain w-full h-auto"
                    priority
                  />
                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/60 text-xs text-gray-200 px-3 py-1 rounded-full shadow-md mt-2">Scaling Plan Example</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scaling Scenarios Section */}
        <div className="mb-20">
          <div className="bg-[#0F1A2A]/80 rounded-3xl border border-purple-900/30 shadow-2xl p-8 md:p-14 backdrop-blur-xl animate-fadeIn">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">Scenarios of Possible FXentra Account Growth</h2>
            <p className="text-lg text-gray-300 mb-8 max-w-4xl">
              The following table demonstrates a scenario of the account balance and risk parameters development on the FXentra Account in case the client successfully passes the Evaluation Process, reaches a maximum FXentra Account allocation of $400,000 (for example, by merging two $200,000 accounts) and fulfils the conditions for Scaling plan:
            </p>
            {/* Currency Selector */}
            <div className="flex justify-center mb-8">
              <div className="bg-[#1A2332]/80 rounded-lg p-1 border border-gray-700/50 shadow-md flex gap-1">
                {currencies.map((currency) => (
                  <button
                    key={currency.code}
                    onClick={() => setSelectedCurrency(currency.code)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-teal-400/60 ${
                      selectedCurrency === currency.code
                        ? 'bg-gradient-to-r from-teal-500 to-blue-500 text-white shadow-lg scale-105'
                        : 'text-gray-400 hover:text-white hover:bg-[#2A3342]'
                    }`}
                  >
                    {currency.name}
                  </button>
                ))}
              </div>
            </div>
            {/* Scaling Table */}
            <div className="overflow-x-auto">
              <table className="w-full rounded-2xl overflow-hidden shadow-xl backdrop-blur-xl bg-white/5">
                <thead>
                  <tr className="border-b border-gray-700/50 bg-gradient-to-r from-teal-900/30 to-blue-900/30">
                    <th className="text-left py-4 px-6 text-gray-300 font-semibold">Elapsed Time</th>
                    <th className="text-left py-4 px-6 text-gray-300 font-semibold">Initial Balance</th>
                    <th className="text-left py-4 px-6 text-gray-300 font-semibold">Maximum Daily Loss</th>
                    <th className="text-left py-4 px-6 text-gray-300 font-semibold">Maximum Loss</th>
                  </tr>
                </thead>
                <tbody>
                  {scalingData.map((row, index) => (
                    <tr
                      key={index}
                      className={`border-b border-gray-700/30 transition-colors duration-200 ${index === 0 ? 'bg-gradient-to-r from-teal-900/10 to-blue-900/10' : 'hover:bg-[#1A2332]/30'}`}
                    >
                      <td className="py-4 px-6 text-gray-300 font-medium">{row.elapsedTime}</td>
                      <td className="py-4 px-6 text-teal-400 font-semibold">{formatCurrency(row.initialBalance)}</td>
                      <td className="py-4 px-6 text-blue-400 font-semibold">{formatCurrency(row.maxDailyLoss)}</td>
                      <td className="py-4 px-6 text-red-400 font-semibold">{formatCurrency(row.maxLoss)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center animate-fadeIn">
          <div className="bg-gradient-to-r from-teal-700/80 via-blue-700/80 to-purple-700/80 rounded-3xl border border-teal-900/30 shadow-2xl p-10 md:p-16 flex flex-col items-center gap-6">
            <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-2 drop-shadow-lg">Discover your Potential and become an FXentra Trader</h2>
            <p className="text-lg text-gray-200 mb-6 max-w-2xl mx-auto">Start your journey with FXentra and unlock the potential to scale your account up to $2,000,000 with our comprehensive scaling plan.</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/pricing"
                className="inline-flex items-center px-10 py-4 bg-gradient-to-r from-teal-400 to-blue-500 hover:from-teal-500 hover:to-blue-600 text-white font-bold rounded-full shadow-lg transition-all duration-300 hover:scale-105 text-lg"
              >
                FXentra Challenge
              </Link>
            </div>
            <div className="mt-8 flex flex-col items-center">
              <span className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-teal-500/20 to-blue-500/20 border border-teal-500/30 text-teal-200 text-sm font-semibold shadow-md backdrop-blur-md">
                <svg className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                Trusted by hundreds of traders
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Animations */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: none; }
        }
        .animate-fadeIn {
          animation: fadeIn 1.2s cubic-bezier(0.22, 1, 0.36, 1) both;
        }
      `}</style>
    </div>
  );
} 