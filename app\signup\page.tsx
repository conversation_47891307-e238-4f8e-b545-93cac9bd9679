'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { createPortal } from 'react-dom';
import { authService } from '@/services/api';
import toast, { Toaster } from 'react-hot-toast';
import Select from 'react-select';
import { countries as countriesList } from 'countries-list';

// Convert countries-list to the format we need
const countries = Object.entries(countriesList).map(([code, country]) => ({
  value: code,
  label: country.name,
  dialCode: country.phone,
  native: country.native,
  capital: country.capital,
  continent: country.continent,
  currency: country.currency,
  languages: country.languages
})).sort((a, b) => a.label.localeCompare(b.label)); // Sort alphabetically by country name

// Custom styles for react-select
const customStyles = {
  control: (base: any, state: any) => ({
    ...base,
    background: 'rgba(31, 41, 55, 0.5)',
    borderColor: state.isFocused ? 'rgba(20, 184, 166, 0.4)' : 'rgba(20, 184, 166, 0.3)',
    boxShadow: state.isFocused ? '0 0 0 2px rgba(20, 184, 166, 0.2)' : 'none',
    '&:hover': {
      borderColor: 'rgba(20, 184, 166, 0.4)'
    },
    borderRadius: '0.5rem',
    minHeight: '48px',
  }),
  menu: (base: any) => ({
    ...base,
    background: 'rgba(31, 41, 55, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(20, 184, 166, 0.2)',
    borderRadius: '0.5rem',
    zIndex: 50,
  }),
  option: (base: any, state: any) => ({
    ...base,
    backgroundColor: state.isFocused ? 'rgba(20, 184, 166, 0.2)' : 'transparent',
    color: state.isFocused ? 'white' : 'rgb(156, 163, 175)',
    '&:active': {
      backgroundColor: 'rgba(20, 184, 166, 0.3)',
    },
    cursor: 'pointer',
  }),
  singleValue: (base: any) => ({
    ...base,
    color: 'white',
  }),
  input: (base: any) => ({
    ...base,
    color: 'white',
  }),
  placeholder: (base: any) => ({
    ...base,
    color: 'rgb(156, 163, 175)',
  }),
};

export default function SignupPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullName: '',
    username: '',
    email: '',
    password: '',
    country: 'US',
    phoneNumber: '',
    address: '',
    city: '',
    state: '',
    zipCode: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(countries[0]);
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
  const [validationState, setValidationState] = useState({
    fullName: { valid: true, message: '' },
    username: { valid: true, message: '' },
    email: { valid: true, message: '' },
    password: { valid: true, message: '' },
    phoneNumber: { valid: true, message: '' },
  });

  // Add field focus tracking for animation
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Password strength tracking
  const [passwordStrength, setPasswordStrength] = useState(0);

  // Add function to check password strength
  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (password.match(/[A-Z]/)) strength += 1;
    if (password.match(/[0-9]/)) strength += 1;
    if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
    return strength;
  };

  // Update password strength when password changes
  useEffect(() => {
    setPasswordStrength(checkPasswordStrength(formData.password));
  }, [formData.password]);

  // Field validation function
  const validateField = (name: string, value: string) => {
    let valid = true;
    let message = '';

    switch (name) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        valid = emailRegex.test(value);
        message = valid ? '' : 'Please enter a valid email address';
        break;
      case 'password':
        valid = value.length >= 8;
        message = valid ? '' : 'Password must be at least 8 characters long';
        break;
      case 'username':
        valid = value.length >= 3;
        message = valid ? '' : 'Username must be at least 3 characters long';
        break;
      case 'fullName':
        valid = value.trim().length > 0;
        message = valid ? '' : 'Please enter your full name';
        break;
      case 'phoneNumber':
        valid = value.trim().length > 0;
        message = valid ? '' : 'Please enter your phone number';
        break;
      case 'address':
        valid = value.trim().length > 0;
        message = valid ? '' : 'Please enter your address';
        break;
    }

    setValidationState(prev => ({
      ...prev,
      [name]: { valid, message }
    }));

    return valid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    const updatedValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: updatedValue
    }));

    // Validate fields as user types
    if (typeof updatedValue === 'string' && ['email', 'username', 'password'].includes(name)) {
      validateField(name, updatedValue);
    }
  };

  // Focus and blur handlers for field animations
  const handleFocus = (fieldName: string) => {
    setFocusedField(fieldName);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFocusedField(null);

    // Validate field on blur
    if (['fullName', 'username', 'email', 'password', 'phoneNumber', 'address'].includes(name)) {
      validateField(name, value);
    }
  };

  const handleCountrySelect = (country: typeof countries[0]) => {
    setSelectedCountry(country);
    setFormData(prev => ({
      ...prev,
      country: country.value
    }));
    setIsCountryDropdownOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if all required fields are filled
    const requiredFields = ['fullName', 'username', 'email', 'password', 'country', 'phoneNumber', 'address'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);

    if (missingFields.length > 0) {
      const errorMsg = `Please fill in all required fields: ${missingFields.join(', ')}`;
      setError(errorMsg);

      // Show error toast for missing fields
      toast.error(errorMsg, {
        style: {
          background: 'rgba(0, 0, 0, 0.9)',
          color: '#ff4e4e',
          border: '1px solid rgba(255, 78, 78, 0.3)',
        },
        duration: 4000,
      });
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Prepare the data for the API
      const signupData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        name: formData.fullName,
        phone_no: formData.phoneNumber,
        country: selectedCountry.label,
        address: formData.address
      };

      // Call the signup API
      await authService.signup(signupData);

      // Show success toast
      toast.success('Account created successfully! Please check your email for verification.', {
        style: {
          background: 'rgba(0, 0, 0, 0.9)',
          color: '#fff',
          border: '1px solid rgba(20, 184, 166, 0.3)',
        },
        icon: '🎉',
        duration: 5000,
      });

      // Store email in localStorage for verification page
      localStorage.setItem('user_email', formData.email);

      // Redirect to email verification page after a short delay
      setTimeout(() => {
      router.push('/verify-email');
      }, 2000);
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to register. Please try again.';
      setError(errorMessage);

      // Show error toast
      toast.error(errorMessage, {
        style: {
          background: 'rgba(0, 0, 0, 0.9)',
          color: '#ff4e4e',
          border: '1px solid rgba(255, 78, 78, 0.3)',
        },
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    setPortalContainer(document.body);
  }, []);

  // Dropdown position tracking
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  const updateDropdownPosition = () => {
    if (countryDropdownRef.current) {
      const rect = countryDropdownRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  };

  // Update dropdown position when it's opened
  useEffect(() => {
    if (isCountryDropdownOpen) {
      updateDropdownPosition();
      window.addEventListener('resize', updateDropdownPosition);
      window.addEventListener('scroll', updateDropdownPosition);
    }

    return () => {
      window.removeEventListener('resize', updateDropdownPosition);
      window.removeEventListener('scroll', updateDropdownPosition);
    };
  }, [isCountryDropdownOpen]);

  // Progress indicator
  const getProgress = () => {
    const filledFields = Object.values(formData).filter(value => value !== '').length;
    return filledFields / Object.keys(formData).length;
  };

  const getProgressText = () => {
    const filledFields = Object.values(formData).filter(value => value !== '').length;
    return `${filledFields}/${Object.keys(formData).length}`;
  };

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center py-6 px-4">
      {/* Toast Container */}
      <Toaster position="top-right" />

      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900 to-teal-900/50 z-0">
        {/* Animated grid background */}
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: 'linear-gradient(to right, rgba(0, 255, 209, 0.15) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 255, 209, 0.15) 1px, transparent 1px)',
          backgroundSize: '40px 40px'
        }}></div>

        {/* Background glow effects */}
        <div className="absolute top-0 left-1/4 w-[500px] h-[500px] bg-teal-400/10 rounded-full blur-[120px] animate-pulse-slow"></div>
        <div className="absolute bottom-0 right-1/4 w-[500px] h-[500px] bg-teal-500/5 rounded-full blur-[120px] animate-pulse-slow-delay"></div>

        {/* Floating particles */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-teal-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute top-40 right-40 w-3 h-3 bg-teal-500 rounded-full animate-float-delay opacity-60"></div>
        <div className="absolute bottom-40 left-1/3 w-2 h-2 bg-teal-400 rounded-full animate-float-reverse opacity-40"></div>
        <div className="absolute bottom-20 right-1/3 w-2 h-2 bg-teal-300 rounded-full animate-float-delay-reverse opacity-70"></div>
      </div>

      {/* Signup Form Content */}
      <div className="relative py-14 px-4 sm:px-6 lg:px-8 z-10 flex justify-center">
        <div className="max-w-xl w-full mx-auto bg-gray-900/30 backdrop-blur-xl rounded-3xl border border-teal-500/20 p-6 md:p-8 shadow-2xl">
          {/* Card glow effect */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-teal-400/5 to-teal-600/5"></div>

          {/* Logo */}
          <div className="mb-6 text-center">
            <div className="flex justify-center mb-4">
              <Image
                src="/images/fxentra-logo.png"
                alt="FXentra Logo"
                width={150}
                height={55}
                className="object-contain"
              />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent mb-3">Create Your Account</h2>
            <div className="w-20 h-1 bg-gradient-to-r from-teal-300 to-teal-500 rounded-full mx-auto"></div>
            <p className="text-gray-400 mt-4 text-sm">
              Join our platform and get started today
            </p>
          </div>

          {/* Display error message */}
        {error && (
            <div className="mb-8 px-5 py-4 rounded-lg bg-red-900/20 border border-red-600/30 text-red-400 text-sm animate-fade-in">
              <div className="flex items-center">
                <svg className="h-5 w-5 mr-3 text-red-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
            {error}
              </div>
          </div>
        )}

          <form className="space-y-8" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Your Name */}
              <div className="space-y-2">
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-300 mb-2">
                  Your Name <span className="text-teal-500">*</span>
                </label>
                <div className="relative group">
                <input
                    id="fullName"
                    name="fullName"
                  type="text"
                  required
                    placeholder="Your full name"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                    value={formData.fullName}
                  onChange={handleChange}
                    onFocus={() => handleFocus('fullName')}
                    onBlur={handleBlur}
                />
                  {!validationState.fullName.valid && (
                    <p className="mt-1 text-sm text-red-400">{validationState.fullName.message}</p>
                  )}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* Username */}
              <div className="space-y-2">
                <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">
                  Username <span className="text-teal-500">*</span>
                </label>
                <div className="relative group">
                <input
                    id="username"
                    name="username"
                  type="text"
                  required
                    placeholder="Choose a username"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                    value={formData.username}
                  onChange={handleChange}
                    onFocus={() => handleFocus('username')}
                    onBlur={handleBlur}
                />
                  {!validationState.username.valid && (
                    <p className="mt-1 text-sm text-red-400">{validationState.username.message}</p>
                  )}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

              {/* Email */}
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Your Email <span className="text-teal-500">*</span>
              </label>
                <div className="relative group">
              <input
                id="email"
                name="email"
                type="email"
                required
                    placeholder="Your email address"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                value={formData.email}
                onChange={handleChange}
                    onFocus={() => handleFocus('email')}
                    onBlur={handleBlur}
              />
                  {!validationState.email.valid && (
                    <p className="mt-1 text-sm text-red-400">{validationState.email.message}</p>
                  )}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
            </div>

              {/* Password with strength meter */}
              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password <span className="text-teal-500">*</span>
              </label>
                <div className="relative group">
              <input
                id="password"
                name="password"
                    type={showPassword ? "text" : "password"}
                required
                    placeholder="Your password"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50 pr-12"
                value={formData.password}
                onChange={handleChange}
                    onFocus={() => handleFocus('password')}
                    onBlur={handleBlur}
                minLength={8}
              />
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-400 focus:outline-none transition-colors z-10"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
                {!validationState.password.valid && (
                  <p className="mt-1 text-sm text-red-400">{validationState.password.message}</p>
                )}

                {/* Password strength meter */}
                <div className="mt-2">
                  <div className="h-1 rounded-full bg-gray-700 overflow-hidden">
                    <div
                      className={`h-full transition-all duration-500 ${
                        !formData.password ? 'w-0 bg-gray-700' :
                        formData.password.length < 5 ? 'w-1/4 bg-red-500' :
                        formData.password.length < 8 ? 'w-1/2 bg-orange-500' :
                        formData.password.length < 12 ? 'w-3/4 bg-yellow-400' :
                        'w-full bg-teal-500'
                      }`}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {!formData.password ? 'Enter password' :
                      formData.password.length < 5 ? 'Weak - Use at least 8 characters' :
                      formData.password.length < 8 ? 'Fair - Use at least 8 characters' :
                      formData.password.length < 12 ? 'Good - Consider using special characters' :
                      'Strong password'}
                  </p>
                </div>
              </div>

              {/* Country - Enhanced */}
              <div className="space-y-2">
                <label htmlFor="country" className="block text-sm font-medium text-gray-300 mb-2">
                  Country <span className="text-teal-500">*</span>
                </label>
                <div className="relative group">
                  <Select
                    id="country"
                    name="country"
                    value={selectedCountry}
                    onChange={(selected) => {
                      if (selected) {
                        setSelectedCountry(selected);
                        setFormData(prev => ({
                          ...prev,
                          country: selected.value
                        }));
                      }
                    }}
                    options={countries}
                    styles={customStyles}
                    placeholder="Select your country"
                    isSearchable
                    required
                    className="react-select-container"
                    classNamePrefix="react-select"
                  />
                </div>
            </div>

              {/* Phone Number - Enhanced */}
              <div className="space-y-2">
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-300 mb-2">
                  Phone Number <span className="text-teal-500">*</span>
              </label>
                <div className="relative group">
                  <div className="flex w-full">
                    <div className="flex-shrink-0 flex items-center bg-gray-800/70 border border-teal-500/30 rounded-l-lg px-3 text-teal-400 font-medium">
                      {selectedCountry.dialCode}
                    </div>
              <input
                      id="phoneNumber"
                      name="phoneNumber"
                      type="tel"
                required
                      className={`flex-1 min-w-0 bg-gray-800/50 border ${
                        validationState.phoneNumber.valid ? 'border-teal-500/30 focus:border-teal-500' : 'border-red-700 focus:border-red-500'
                      } border-l-0 rounded-r-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 ${
                        validationState.phoneNumber.valid ? 'focus:ring-teal-500/40' : 'focus:ring-red-500/20'
                      } focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50`}
                      placeholder="Your phone number"
                      value={formData.phoneNumber}
                onChange={handleChange}
                      onFocus={() => handleFocus('phoneNumber')}
                      onBlur={handleBlur}
              />
            </div>
                  {!validationState.phoneNumber.valid && (
                    <p className="mt-1 text-sm text-red-400">{validationState.phoneNumber.message}</p>
                  )}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">We'll send important updates to this number</p>
          </div>

              {/* Address with enhanced styling */}
              <div className="md:col-span-2 space-y-3 mt-4">
                <label htmlFor="address" className="block text-sm font-medium text-gray-300 mb-2">
                  Address <span className="text-teal-500">*</span>
                </label>
                <div className="relative group">
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-teal-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
            <input
                    id="address"
                    name="address"
                    type="text"
              required
                    placeholder="Your address"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 pl-12 pr-5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                    value={formData.address}
                    onChange={handleChange}
                    onFocus={() => handleFocus('address')}
                    onBlur={handleBlur}
                  />
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              {/* Location Fields with animated reveal */}
              <div className="space-y-2">
                <label htmlFor="city" className="flex items-center text-sm font-medium text-gray-300 mb-2">
                  <span>City</span>
                  <span className="ml-1 text-xs font-normal text-gray-500">(Optional)</span>
                </label>
                <div className="relative group">
                  <input
                    id="city"
                    name="city"
                    type="text"
                    placeholder="Your city"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                    value={formData.city}
                    onChange={handleChange}
                    onFocus={() => handleFocus('city')}
                    onBlur={() => setFocusedField(null)}
                  />
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="state" className="flex items-center text-sm font-medium text-gray-300 mb-2">
                  <span>State</span>
                  <span className="ml-1 text-xs font-normal text-gray-500">(Optional)</span>
                </label>
                <div className="relative group">
                  <input
                    id="state"
                    name="state"
                    type="text"
                    placeholder="Your state/province"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                    value={formData.state}
              onChange={handleChange}
                    onFocus={() => handleFocus('state')}
                    onBlur={() => setFocusedField(null)}
                  />
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="zipCode" className="flex items-center text-sm font-medium text-gray-300 mb-2">
                  <span>Zip Code</span>
                  <span className="ml-1 text-xs font-normal text-gray-500">(Optional)</span>
            </label>
                <div className="relative group">
                  <input
                    id="zipCode"
                    name="zipCode"
                    type="text"
                    placeholder="Your zip code"
                    className="w-full bg-gray-800/50 border border-teal-500/30 rounded-lg py-3 px-4 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-500/40 focus:border-transparent transition-all duration-300 group-hover:border-teal-400/50"
                    value={formData.zipCode}
                    onChange={handleChange}
                    onFocus={() => handleFocus('zipCode')}
                    onBlur={() => setFocusedField(null)}
                  />
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              </div>
            </div>

            {/* Security notice */}
            <div className="flex items-center bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-6 mt-8">
              <div className="flex-shrink-0 mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div className="text-sm text-gray-400">
                Your data is securely encrypted and protected. We never share your information with third parties without your consent.
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-4 px-6 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold rounded-xl shadow-lg shadow-teal-500/30 hover:shadow-teal-500/50 transition-all duration-300 transform hover:translate-y-[-2px] ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Account...
                </div>
              ) : (
                'Create Account'
              )}
            </button>

            <div className="mt-10 text-center text-sm text-gray-400 relative z-10">
              Already have an account?{' '}
              <Link href="/login" className="text-teal-400 hover:text-teal-300 font-medium transition-colors">
                Sign in
              </Link>
            </div>

            {/* Add an asterisk explanation at the bottom of the form */}
            <div className="text-xs text-gray-500 mt-6 text-center">
              <p><span className="text-teal-500">*</span> Required fields</p>
          </div>
        </form>
        </div>
      </div>

      {/* Custom animations and styles */}
      <style jsx global>{`
        @keyframes pulse-very-slow {
          0%, 100% { opacity: 0; }
          50% { opacity: 0.3; }
        }

        @keyframes pulse-slow {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.5; }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
        }

        @keyframes float-delay {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
          animation-delay: 1s;
        }

        @keyframes float-reverse {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(15px); }
        }

        @keyframes float-delay-reverse {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(15px); }
          animation-delay: 1.5s;
        }

        .animate-pulse-very-slow {
          animation: pulse-very-slow 10s ease-in-out infinite;
        }

        .animate-pulse-slow {
          animation: pulse-slow 6s ease-in-out infinite;
        }

        .animate-pulse-slow-delay {
          animation: pulse-slow 6s ease-in-out infinite;
          animation-delay: 3s;
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .animate-float-delay {
          animation: float-delay 7s ease-in-out infinite;
          animation-delay: 2s;
        }

        .animate-float-reverse {
          animation: float-reverse 8s ease-in-out infinite;
        }

        .animate-float-delay-reverse {
          animation: float-delay-reverse 9s ease-in-out infinite;
          animation-delay: 3s;
        }

        .animate-fade-in {
          animation: fadeIn 0.4s ease-out forwards;
        }

        @keyframes fadeIn {
          0% { opacity: 0; transform: translateY(-5px); }
          100% { opacity: 1; transform: translateY(0); }
        }
      `}</style>
    </div>
  );
}