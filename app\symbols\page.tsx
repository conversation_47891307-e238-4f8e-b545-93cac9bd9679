"use client";
import React, { useEffect } from "react";

export default function SymbolsPage() {
  useEffect(() => {
    // Load TradingView widget
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-symbol-overview.js';
    script.async = true;
    script.innerHTML = JSON.stringify({
      "symbols": [
        [
          "FOREXCOM:EURUSD",
          "EURUSD"
        ],
        [
          "FOREXCOM:GBPUSD",
          "GBPUSD"
        ],
        [
          "FOREXCOM:USDJPY",
          "USDJPY"
        ],
        [
          "FOREXCOM:USDCHF",
          "USDCHF"
        ],
        [
          "FOREXCOM:AUDUSD",
          "AUDUSD"
        ],
        [
          "FOREXCOM:USDCAD",
          "USDCAD"
        ],
        [
          "CRYPTOCAP:BTC",
          "BTCUSD"
        ],
        [
          "CRYPTOCAP:ETH",
          "ETHUSD"
        ]
      ],
      "chartOnly": false,
      "width": "100%",
      "height": "600",
      "locale": "en",
      "colorTheme": "dark",
      "autosize": true,
      "showVolume": false,
      "showMA": false,
      "hideDateRanges": false,
      "hideMarketStatus": false,
      "hideSymbolLogo": false,
      "scalePosition": "right",
      "scaleMode": "Normal",
      "fontFamily": "-apple-system, BlinkMacSystemFont, Trebuchet MS, Roboto, Ubuntu, sans-serif",
      "fontSize": "10",
      "noTimeScale": false,
      "valuesTracking": "1",
      "changeMode": "price-and-percent",
      "chartType": "area",
      "maLineColor": "#2962FF",
      "maLineWidth": 1,
      "maLength": 9,
      "backgroundColor": "rgba(19, 23, 34, 1)",
      "widgetFontColor": "rgba(255, 255, 255, 1)",
      "gridLineColor": "rgba(42, 46, 57, 1)",
      "borderColor": "rgba(42, 46, 57, 1)",
      "borderWidth": 1,
      "sessionsMode": "extended"
    });

    const container = document.getElementById('tradingview-widget');
    if (container) {
      container.innerHTML = '';
      container.appendChild(script);
    }

    return () => {
      if (container) {
        container.innerHTML = '';
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-[#030609] relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200">
      {/* Background elements matching landing page */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-blue-500/10 to-teal-500/10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#030609]/20 to-[#030609]/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center relative">
            {/* Decorative elements */}
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-teal-400 to-transparent opacity-60"></div>
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-40"></div>
            
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-500/20 to-blue-500/20 border border-teal-500/30 text-teal-300 text-sm font-medium mb-6 backdrop-blur-sm">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Real-Time Data
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white via-teal-100 to-white bg-clip-text text-transparent">
                Live Currency
              </span>
              <br />
              <span className="bg-gradient-to-r from-teal-400 via-blue-400 to-teal-400 bg-clip-text text-transparent">
                Symbols
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light">
              Monitor real-time 
              <span className="text-teal-400 font-medium"> forex pairs</span> and 
              <span className="text-blue-400 font-medium"> cryptocurrency prices</span> with 
              <span className="text-teal-400 font-medium"> professional-grade charts</span> and market data.
            </p>
            
            {/* Stats */}
            <div className="flex justify-center items-center space-x-8 mt-12 pt-8 border-t border-gray-700/30">
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">8+</div>
                <div className="text-sm text-gray-400">Major Pairs</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">24/7</div>
                <div className="text-sm text-gray-400">Live Updates</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">Real-time</div>
                <div className="text-sm text-gray-400">Charts</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* TradingView Widget */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
        <div className="bg-[#101C2C] rounded-2xl border border-gray-700/50 shadow-xl backdrop-blur-sm overflow-hidden">
          <div id="tradingview-widget" className="w-full h-[600px]"></div>
        </div>
      </div>
    </div>
  );
} 