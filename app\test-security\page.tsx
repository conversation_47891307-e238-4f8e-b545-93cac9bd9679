'use client';

import { useState } from 'react';
import { directApiCall } from '@/services/api';

export default function TestSecurityPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testApiCall = async () => {
    setLoading(true);
    setResult('');

    try {
      console.log('Making test API call...');
      // Test both auth and order endpoints
      await directApiCall('auth/login', {
        method: 'POST',
        data: new FormData() // Empty form data for testing
      });
      
      await directApiCall('order/orders', {
        method: 'GET'
      });

      setResult('✅ Check console for exact URLs used in the requests');
    } catch (error) {
      console.log('Expected error (auth/permission denied)');
      setResult('✅ Check console for exact URLs used in the requests (auth error expected)');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">API URL Test Page</h1>
      
      <div className="space-y-4">
        <button
          onClick={testApiCall}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test API URLs'}
        </button>

        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p>{result}</p>
          </div>
        )}

        <div className="mt-8 space-y-4">
          <h3 className="text-lg font-semibold">Expected URLs:</h3>
          <div className="space-y-2 font-mono text-sm bg-gray-800 text-white p-4 rounded">
            <p>Proxy URL: /auth/login</p>
            <p>Backend URL: https://fxentra-ec0dfccfb73c.herokuapp.com/auth/login</p>
          </div>
        </div>
      </div>
    </div>
  );
}
