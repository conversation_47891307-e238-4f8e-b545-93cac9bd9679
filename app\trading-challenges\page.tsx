'use client';

import { useState } from 'react';

interface Challenge {
  title: string;
  description: string;
  features: string[];
  price: string;
  popular?: boolean;
  accountType: 'instant' | 'oneStep' | 'twoStep';
  maxDrawdown: string;
  dailyDrawdown: string;
  profitTarget: string;
  profitShare: string;
  duration: string;
}

const challenges: Challenge[] = [
  {
    title: "Instant Funded Account",
    description: "Start trading immediately with our capital",
    accountType: 'instant',
    features: [
      "No evaluation phase",
      "Immediate funding",
      "Real money account",
      "Bi-weekly payouts",
      "90% profit share",
      "No time limits"
    ],
    maxDrawdown: "5%",
    dailyDrawdown: "2.5%",
    profitTarget: "No target",
    profitShare: "90%",
    duration: "Immediate",
    price: "$599"
  },
  {
    title: "One-Step Challenge",
    description: "Single phase evaluation for experienced traders",
    accountType: 'oneStep',
    features: [
      "Single evaluation phase",
      "10% profit target",
      "Bi-weekly payouts",
      "90% profit share",
      "Scaling opportunities",
      "News trading allowed"
    ],
    maxDrawdown: "8%",
    dailyDrawdown: "4%",
    profitTarget: "10%",
    profitShare: "90%",
    duration: "30 days",
    price: "$249",
    popular: true
  },
  {
    title: "Two-Step Challenge",
    description: "Our most thorough evaluation process",
    accountType: 'twoStep',
    features: [
      "Two evaluation phases",
      "Lower initial cost",
      "Bi-weekly payouts",
      "80% profit share",
      "Scaling opportunities",
      "News trading allowed"
    ],
    maxDrawdown: "10%",
    dailyDrawdown: "4%",
    profitTarget: "10% & 5%",
    profitShare: "80%",
    duration: "60 days",
    price: "$199"
  }
];

export default function TradingChallengesPage() {
  const [selectedType, setSelectedType] = useState<'instant' | 'oneStep' | 'twoStep'>('oneStep');

  return (
    <div className="min-h-screen bg-[#0A1118]">
      {/* Professional Header Section */}
      <div className="relative bg-[#0F1A2E] border-b border-teal-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="inline-block px-3 py-1 rounded-full bg-gradient-to-r from-teal-500/10 to-blue-500/10 backdrop-blur-sm border border-teal-500/20 mb-4">
              <span className="text-teal-400 font-medium text-sm uppercase tracking-widest">Trading Challenges</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300 mt-4 mb-6">
              Choose Your Path to Funded Trading
            </h1>
            <div className="h-1 w-20 mx-auto bg-gradient-to-r from-teal-500 to-teal-300 rounded-full mb-6"></div>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto leading-relaxed">
              Select your preferred challenge type and start your journey to becoming a funded trader today.
            </p>
          </div>
        </div>

        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'40\' height=\'40\' viewBox=\'0 0 40 40\'%3E%3Cg fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zm33.66 30.1l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zm-14.66 0l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zM1.41 22l2.83-2.83 1.41 1.41L2.83 23.41 1.41 22zm32.25-18.24l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zm-14.66 0l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zM1.41 4.24l2.83-2.83 1.41 1.41L2.83 5.65 1.41 4.24z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
              backgroundSize: '24px 24px'
            }}
          />
          
          {/* Radial gradient background effect */}
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[800px] h-[800px] rounded-full bg-teal-500/5 blur-[120px] pointer-events-none" />
          
          {/* Decorative elements */}
          <div className="absolute top-40 left-10 w-64 h-64 bg-teal-400/5 rounded-full blur-[80px]" />
          <div className="absolute bottom-40 right-10 w-80 h-80 bg-blue-400/5 rounded-full blur-[100px]" />
          <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-teal-300 rounded-full shadow-[0_0_10px_4px_rgba(45,212,191,0.3)]" />
          <div className="absolute bottom-1/3 left-1/4 w-3 h-3 bg-teal-300 rounded-full shadow-[0_0_15px_5px_rgba(45,212,191,0.25)]" />
        </div>
      </div>

      {/* Challenge Type Selection */}
      <div className="container max-w-6xl mx-auto px-4 py-16 relative z-10">
        <div className="flex justify-center mb-12">
          <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-1.5 inline-flex">
            {[
              { type: 'instant', label: 'Instant Funded' },
              { type: 'oneStep', label: 'One-Step' },
              { type: 'twoStep', label: 'Two-Step' }
            ].map(({ type, label }) => (
              <button
                key={type}
                onClick={() => setSelectedType(type as typeof selectedType)}
                className={`px-6 py-2.5 rounded-lg transition-all duration-300 ${
                  selectedType === type
                    ? 'bg-gradient-to-r from-teal-500 to-teal-400 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Challenges Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {challenges
            .filter(challenge => !selectedType || challenge.accountType === selectedType)
            .map((challenge, index) => (
            <div
              key={index}
              className={`relative group ${
                challenge.popular ? 'md:scale-105 md:-translate-y-2' : ''
              }`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-blue-500/5 rounded-2xl transform transition-all duration-300 group-hover:scale-[1.02] group-hover:from-teal-500/10 group-hover:to-blue-500/10"></div>
              <div className={`absolute inset-0 border rounded-2xl transition-colors duration-300 ${
                challenge.popular ? 'border-teal-500/50' : 'border-gray-800/50 group-hover:border-teal-500/30'
              }`}></div>
              
              <div className="relative p-6 rounded-2xl backdrop-blur-sm bg-gray-900/30">
                {challenge.popular && (
                  <div className="absolute -top-3 right-4">
                    <span className="bg-teal-500 text-black text-sm font-medium px-3 py-1 rounded-full">
                      Most Popular
                    </span>
                  </div>
                )}

                <h3 className="text-xl font-bold text-white mb-2">{challenge.title}</h3>
                <p className="text-gray-400 mb-6">{challenge.description}</p>

                <div className="space-y-6">
                  {/* Trading Parameters */}
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Max Drawdown</span>
                      <span className="text-teal-400 font-medium">{challenge.maxDrawdown}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Daily Drawdown</span>
                      <span className="text-teal-400 font-medium">{challenge.dailyDrawdown}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Profit Target</span>
                      <span className="text-teal-400 font-medium">{challenge.profitTarget}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Profit Share</span>
                      <span className="text-teal-400 font-medium">{challenge.profitShare}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Duration</span>
                      <span className="text-teal-400 font-medium">{challenge.duration}</span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3">
                    {challenge.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start">
                        <svg
                          className="w-5 h-5 text-teal-500 mt-0.5 mr-2 shrink-0"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Price and CTA */}
                  <div>
                    <div className="mb-4">
                      <span className="text-3xl font-bold text-white">{challenge.price}</span>
                      <span className="text-gray-400 ml-2">one-time fee</span>
                    </div>
                    <button className="w-full bg-gradient-to-r from-teal-500 to-teal-400 hover:from-teal-400 hover:to-teal-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg hover:shadow-teal-500/25">
                      Start Challenge
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="mt-20 text-center relative">
          <div className="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-blue-500/5 rounded-2xl -z-10"></div>
          <div className="py-10 px-8 rounded-2xl border border-gray-800/40 backdrop-blur-sm">
            <h3 className="text-2xl font-semibold text-white mb-4">Ready to Start Your Trading Journey?</h3>
            <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
              Choose your preferred challenge type and begin your path to becoming a funded trader today.
            </p>
            <a 
              href="#footer" 
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-teal-500 to-teal-400 text-white font-medium rounded-full transition-all duration-300 hover:shadow-[0_0_20px_rgba(20,184,166,0.4)] hover:scale-105"
            >
              Contact Support
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
} 