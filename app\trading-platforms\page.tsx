'use client';

import { useState } from 'react';
import Link from 'next/link';

interface PlatformFeature {
  icon: string;
  title: string;
  description: string;
}

interface PlatformInfo {
  name: string;
  version: string;
  description: string;
  logo: string;
  features: PlatformFeature[];
  benefits: string[];
  systemRequirements: {
    os: string[];
    ram: string;
    storage: string;
    internet: string;
  };
  downloadLinks: {
    windows: string;
    mac: string;
    mobile: {
      ios: string;
      android: string;
    };
  };
}

const mt4Features: PlatformFeature[] = [
  {
    icon: "📊",
    title: "Advanced Charting",
    description: "Professional charts with 9 timeframes and 30+ built-in indicators"
  },
  {
    icon: "⚡",
    title: "Fast Execution",
    description: "Lightning-fast order execution with minimal slippage"
  },
  {
    icon: "🤖",
    title: "Expert Advisors",
    description: "Automated trading with custom EAs and algorithmic strategies"
  },
  {
    icon: "📱",
    title: "Mobile Trading",
    description: "Trade anywhere with MT4 mobile apps for iOS and Android"
  },
  {
    icon: "📈",
    title: "Market Analysis",
    description: "Real-time market data and comprehensive analysis tools"
  },
  {
    icon: "🔒",
    title: "Secure Trading",
    description: "Bank-level security with encrypted data transmission"
  }
];

const mt5Features: PlatformFeature[] = [
  {
    icon: "📊",
    title: "Enhanced Charting",
    description: "21 timeframes and 80+ built-in technical indicators"
  },
  {
    icon: "📈",
    title: "Economic Calendar",
    description: "Built-in economic calendar with market impact analysis"
  },
  {
    icon: "🤖",
    title: "Advanced EAs",
    description: "Enhanced Expert Advisors with MQL5 programming"
  },
  {
    icon: "📱",
    title: "Multi-Platform",
    description: "Desktop, web, and mobile versions with sync"
  },
  {
    icon: "💼",
    title: "Multi-Asset Trading",
    description: "Trade forex, stocks, futures, and CFDs in one platform"
  },
  {
    icon: "🔧",
    title: "Advanced Tools",
    description: "Strategy tester, market depth, and hedging capabilities"
  }
];

const platforms: Record<string, PlatformInfo> = {
  mt4: {
    name: "MetaTrader 4",
    version: "Build 1400",
    description: "The world's most popular trading platform, trusted by millions of traders worldwide. MT4 offers intuitive interface, powerful charting tools, and automated trading capabilities.",
    logo: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
    features: mt4Features,
    benefits: [
      "User-friendly interface perfect for beginners and professionals",
      "Extensive library of free and paid indicators",
      "One-click trading with instant execution",
      "Advanced order types including stop loss and take profit",
      "Real-time price quotes and market news",
      "Customizable workspace and chart layouts",
      "Multi-language support in 40+ languages",
      "Free demo account for practice trading"
    ],
    systemRequirements: {
      os: ["Windows 7/8/10/11", "macOS 10.12+", "Linux (Wine)"],
      ram: "512 MB RAM minimum",
      storage: "50 MB free disk space",
      internet: "Broadband internet connection"
    },
    downloadLinks: {
      windows: "https://download.mql5.com/cdn/web/metaquotes.software.corp/mt4/mt4setup.exe",
      mac: "https://download.mql5.com/cdn/web/metaquotes.software.corp/mt4/mt4setup.dmg",
      mobile: {
        ios: "https://apps.apple.com/app/metatrader-4/id496212596",
        android: "https://play.google.com/store/apps/details?id=net.metaquotes.metatrader4"
      }
    }
  },
  mt5: {
    name: "MetaTrader 5",
    version: "Build 4000",
    description: "The next generation trading platform with enhanced features, advanced charting, and multi-asset trading capabilities. MT5 is designed for serious traders who demand more.",
    logo: "https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png",
    features: mt5Features,
    benefits: [
      "Advanced charting with 21 timeframes and 80+ indicators",
      "Multi-asset trading: forex, stocks, futures, and CFDs",
      "Enhanced Expert Advisors with MQL5 programming",
      "Built-in economic calendar and market analysis",
      "Strategy tester for backtesting and optimization",
      "Market depth and Level II pricing",
      "Hedging and netting account types",
      "Advanced order types and execution modes"
    ],
    systemRequirements: {
      os: ["Windows 7/8/10/11", "macOS 10.12+", "Linux (Wine)"],
      ram: "1 GB RAM minimum",
      storage: "100 MB free disk space",
      internet: "Broadband internet connection"
    },
    downloadLinks: {
      windows: "https://download.mql5.com/cdn/web/metaquotes.software.corp/mt5/mt5setup.exe",
      mac: "https://download.mql5.com/cdn/web/metaquotes.software.corp/mt5/mt5setup.dmg",
      mobile: {
        ios: "https://apps.apple.com/app/metatrader-5/id413251709",
        android: "https://play.google.com/store/apps/details?id=net.metaquotes.metatrader5"
      }
    }
  }
};

export default function TradingPlatformsPage() {
  const [selectedPlatform, setSelectedPlatform] = useState<'mt4' | 'mt5'>('mt4');

  return (
    <div className="min-h-screen bg-[#030609] relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200">
      {/* Background elements matching landing page */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 via-blue-500/10 to-teal-500/10"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#030609]/20 to-[#030609]/40"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center relative">
            {/* Decorative elements */}
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-teal-400 to-transparent opacity-60"></div>
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-40"></div>
            
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-500/20 to-blue-500/20 border border-teal-500/30 text-teal-300 text-sm font-medium mb-6 backdrop-blur-sm">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Professional Trading
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white via-teal-100 to-white bg-clip-text text-transparent">
                Trading
              </span>
              <br />
              <span className="bg-gradient-to-r from-teal-400 via-blue-400 to-teal-400 bg-clip-text text-transparent">
                Platforms
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light">
              Choose from the world's most trusted 
              <span className="text-teal-400 font-medium"> trading platforms</span> with 
              <span className="text-blue-400 font-medium"> advanced features</span> and 
              <span className="text-teal-400 font-medium"> professional tools</span>.
            </p>
            
            {/* Stats */}
            <div className="flex justify-center items-center space-x-8 mt-12 pt-8 border-t border-gray-700/30">
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">2</div>
                <div className="text-sm text-gray-400">Platforms</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">Multi-device</div>
                <div className="text-sm text-gray-400">Support</div>
              </div>
              <div className="w-px h-8 bg-gray-700/50"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-teal-400">Free</div>
                <div className="text-sm text-gray-400">Downloads</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Platform Selection Tabs */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
        <div className="flex justify-center">
          <div className="bg-[#131B29]/70 rounded-2xl p-2 flex border border-gray-700/50 shadow-xl backdrop-blur-sm">
            <button 
              onClick={() => setSelectedPlatform('mt4')}
              className={`px-10 py-4 rounded-xl transition-all duration-500 font-semibold text-base relative overflow-hidden ${
                selectedPlatform === 'mt4' 
                  ? 'bg-gradient-to-r from-teal-600 via-teal-500 to-teal-600 text-white shadow-2xl shadow-teal-500/30 transform scale-105' 
                  : 'text-gray-400 hover:text-white hover:bg-[#1A2332] hover:shadow-lg'
              }`}
            >
              {selectedPlatform === 'mt4' && (
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
              )}
              <span className="relative z-10">MetaTrader 4</span>
            </button>
            <button 
              onClick={() => setSelectedPlatform('mt5')}
              className={`px-10 py-4 rounded-xl transition-all duration-500 font-semibold text-base relative overflow-hidden ${
                selectedPlatform === 'mt5' 
                  ? 'bg-gradient-to-r from-teal-600 via-teal-500 to-teal-600 text-white shadow-2xl shadow-teal-500/30 transform scale-105' 
                  : 'text-gray-400 hover:text-white hover:bg-[#1A2332] hover:shadow-lg'
              }`}
            >
              {selectedPlatform === 'mt5' && (
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
              )}
              <span className="relative z-10">MetaTrader 5</span>
            </button>
          </div>
        </div>
      </div>

      {/* Platform Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-24">
        <div className="bg-[#101C2C] rounded-3xl border border-gray-700/50 shadow-2xl backdrop-blur-sm overflow-hidden">
          {/* Platform Header */}
          <div className="relative p-12 border-b border-gray-700/50 overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute inset-0 bg-gradient-to-br from-teal-500/20 via-transparent to-blue-500/20"></div>
            </div>
            
            <div className="relative flex flex-col lg:flex-row items-center gap-8">
              <div className="flex-shrink-0 relative">
                <div className="w-32 h-32 bg-gradient-to-br from-teal-500/20 to-blue-500/20 rounded-2xl p-4 border border-teal-500/30 shadow-xl">
                  <img 
                    src={platforms[selectedPlatform].logo} 
                    alt={platforms[selectedPlatform].name}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="flex-1 text-center lg:text-left">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-teal-500/20 to-blue-500/20 px-4 py-2 rounded-full border border-teal-500/30 mb-4">
                  <div className="w-2 h-2 bg-teal-400 rounded-full animate-pulse"></div>
                  <span className="text-teal-400 font-medium text-sm">Latest Version</span>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold text-white mb-3">
                  {platforms[selectedPlatform].name}
                </h2>
                <p className="text-teal-400 font-semibold text-lg mb-4">
                  Version {platforms[selectedPlatform].version}
                </p>
                <p className="text-gray-300 text-xl leading-relaxed max-w-3xl">
                  {platforms[selectedPlatform].description}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 p-12">
            {/* Features Section */}
            <div className="relative">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-1 h-8 bg-gradient-to-b from-teal-500 to-blue-500 rounded-full"></div>
                <h3 className="text-3xl font-bold text-white">Key Features</h3>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {platforms[selectedPlatform].features.map((feature, index) => (
                  <div key={index} className="group bg-gradient-to-br from-[#131B29]/80 to-[#1A2332]/80 rounded-2xl p-6 border border-gray-700/30 hover:border-teal-500/50 transition-all duration-500 hover:shadow-xl hover:shadow-teal-500/10 hover:-translate-y-1">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-teal-500/20 to-blue-500/20 rounded-xl flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-300">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white mb-2 text-lg">{feature.title}</h4>
                        <p className="text-gray-300 text-sm leading-relaxed">{feature.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Benefits Section */}
            <div className="relative">
              <div className="flex items-center gap-3 mb-8">
                <div className="w-1 h-8 bg-gradient-to-b from-teal-500 to-blue-500 rounded-full"></div>
                <h3 className="text-3xl font-bold text-white">Platform Benefits</h3>
              </div>
              <div className="space-y-6">
                {platforms[selectedPlatform].benefits.map((benefit, index) => (
                  <div key={index} className="group flex items-start gap-4 p-4 rounded-xl hover:bg-[#131B29]/50 transition-all duration-300">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-teal-500/20 to-blue-500/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-4 h-4 text-teal-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-gray-300 leading-relaxed">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* System Requirements */}
          <div className="p-8 border-t border-gray-700/50">
            <h3 className="text-2xl font-bold text-white mb-6">System Requirements</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-[#131B29]/50 rounded-xl p-4 border border-gray-700/30">
                <h4 className="font-semibold text-teal-400 mb-2">Operating System</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  {platforms[selectedPlatform].systemRequirements.os.map((os, index) => (
                    <li key={index}>• {os}</li>
                  ))}
                </ul>
              </div>
              <div className="bg-[#131B29]/50 rounded-xl p-4 border border-gray-700/30">
                <h4 className="font-semibold text-teal-400 mb-2">RAM</h4>
                <p className="text-gray-300 text-sm">{platforms[selectedPlatform].systemRequirements.ram}</p>
              </div>
              <div className="bg-[#131B29]/50 rounded-xl p-4 border border-gray-700/30">
                <h4 className="font-semibold text-teal-400 mb-2">Storage</h4>
                <p className="text-gray-300 text-sm">{platforms[selectedPlatform].systemRequirements.storage}</p>
              </div>
              <div className="bg-[#131B29]/50 rounded-xl p-4 border border-gray-700/30">
                <h4 className="font-semibold text-teal-400 mb-2">Internet</h4>
                <p className="text-gray-300 text-sm">{platforms[selectedPlatform].systemRequirements.internet}</p>
              </div>
            </div>
          </div>

          {/* Download Section */}
          <div className="p-8 border-t border-gray-700/50">
            <h3 className="text-2xl font-bold text-white mb-6">Download {platforms[selectedPlatform].name}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Desktop Downloads */}
              <div className="bg-[#131B29]/50 rounded-xl p-6 border border-gray-700/30 hover:border-teal-500/30 transition-all duration-300">
                <div className="text-center">
                  <div className="text-3xl mb-3">🖥️</div>
                  <h4 className="font-semibold text-white mb-2">Windows</h4>
                  <a 
                    href={platforms[selectedPlatform].downloadLinks.windows}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-teal-500 hover:bg-teal-600 text-white font-semibold px-4 py-2 rounded-lg transition-colors duration-200"
                  >
                    Download
                  </a>
                </div>
              </div>

              <div className="bg-[#131B29]/50 rounded-xl p-6 border border-gray-700/30 hover:border-teal-500/30 transition-all duration-300">
                <div className="text-center">
                  <div className="text-3xl mb-3">🍎</div>
                  <h4 className="font-semibold text-white mb-2">macOS</h4>
                  <a 
                    href={platforms[selectedPlatform].downloadLinks.mac}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-teal-500 hover:bg-teal-600 text-white font-semibold px-4 py-2 rounded-lg transition-colors duration-200"
                  >
                    Download
                  </a>
                </div>
              </div>

              {/* Mobile Downloads */}
              <div className="bg-[#131B29]/50 rounded-xl p-6 border border-gray-700/30 hover:border-teal-500/30 transition-all duration-300">
                <div className="text-center">
                  <div className="text-3xl mb-3">📱</div>
                  <h4 className="font-semibold text-white mb-2">iOS App</h4>
                  <a 
                    href={platforms[selectedPlatform].downloadLinks.mobile.ios}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-teal-500 hover:bg-teal-600 text-white font-semibold px-4 py-2 rounded-lg transition-colors duration-200"
                  >
                    App Store
                  </a>
                </div>
              </div>

              <div className="bg-[#131B29]/50 rounded-xl p-6 border border-gray-700/30 hover:border-teal-500/30 transition-all duration-300">
                <div className="text-center">
                  <div className="text-3xl mb-3">🤖</div>
                  <h4 className="font-semibold text-white mb-2">Android App</h4>
                  <a 
                    href={platforms[selectedPlatform].downloadLinks.mobile.android}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block bg-teal-500 hover:bg-teal-600 text-white font-semibold px-4 py-2 rounded-lg transition-colors duration-200"
                  >
                    Google Play
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="p-8 border-t border-gray-700/50 bg-gradient-to-r from-teal-900/20 to-blue-900/20">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Ready to Start Trading?</h3>
              <p className="text-gray-300 mb-6">
                Download your preferred platform and connect to your FXentra account to begin trading.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  href="/dashboard/place-order"
                  className="px-8 py-3 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-semibold rounded-lg shadow-lg shadow-teal-500/25 transition-all duration-300"
                >
                  Get Started
                </Link>
                <Link 
                  href="/faqs"
                  className="px-8 py-3 bg-[#131B29] hover:bg-[#1A2332] text-white font-semibold rounded-lg border border-gray-700/50 transition-all duration-300"
                >
                  Learn More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 