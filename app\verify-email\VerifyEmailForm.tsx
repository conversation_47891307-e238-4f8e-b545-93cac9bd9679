'use client';

import React, { useState, useEffect } from 'react';
import { Box, Container, TextField, Typography, Paper } from '@mui/material';
import { useRouter } from 'next/navigation';
import { directApiCall } from '@/services/api';

export default function VerifyEmailForm() {
  const [verificationCode, setVerificationCode] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    // Get email from localStorage
    if (typeof window !== 'undefined') {
      const storedEmail = localStorage.getItem('verification-email');
      if (storedEmail) {
        setEmail(storedEmail);
      }
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const response = await directApiCall(
        `auth/verify-email?email=${localStorage.getItem('user_email')}&code=${verificationCode}`,
        { method: 'POST' }
      );

      if (response) {
        // Handle successful verification
        localStorage.removeItem('verification-email'); // Clean up
        router.push('/login'); // Redirect to login page
      }
    } catch (err) {
      setError('Verification failed. Please check your code and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" className="mt-20">
      <div className="relative">
        {/* Background Glow Effect */}
        <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-teal-400/5 rounded-full blur-[100px] pointer-events-none"></div>
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-teal-400/5 rounded-full blur-[100px] pointer-events-none"></div>

        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-8 relative">
          <Typography variant="h5" align="center" className="text-gray-200 mb-6 font-medium">
            Email Verification
          </Typography>

          {email && (
            <Typography variant="body1" align="center" className="text-gray-400 mb-6">
              Please enter the verification code sent to <span className="text-teal-400">{email}</span>
            </Typography>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative group">
              <input
                type="text"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                className="w-full bg-[#0B1221] text-gray-200 border border-gray-800 rounded-xl px-4 py-3 focus:outline-none focus:border-teal-500/50 transition-colors"
                placeholder="Enter verification code"
                required
                autoFocus
              />
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-teal-500/0 via-teal-500/5 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>

            {error && (
              <Typography color="error" variant="body2" className="text-red-400 text-sm">
                {error}
              </Typography>
            )}

            <button
              type="submit"
              disabled={loading}
              className="relative w-full flex items-center justify-center py-3 px-6 rounded-xl text-white group overflow-hidden"
            >
              {/* Button background with glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-teal-500 to-teal-400 rounded-xl"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-teal-600 to-teal-500 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Button shine effect */}
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-all duration-700 opacity-30 bg-gradient-to-r from-transparent via-white to-transparent rounded-xl"></div>

              {/* Button content */}
              <span className="relative flex items-center">
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Verifying...</span>
                  </>
                ) : (
                  <span className="font-medium">Verify Email</span>
                )}
              </span>
            </button>
          </form>
        </div>
      </div>
    </Container>
  );
}