'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { authService } from '@/services/api';

const DashboardNavbar = () => {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [username, setUsername] = useState<string | null>(null);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // Get username from auth service
  useEffect(() => {
    const storedUsername = authService.getUsername();
    setUsername(storedUsername);
  }, []);

  // Handle logout
  const handleLogout = () => {
    authService.logout();
    router.push('/login');
  };

  return (
    <nav className={`fixed top-0 w-full z-50 transition-all duration-300 ${
      scrolled ? 'bg-dark-lighter/90 backdrop-blur-lg shadow-[0_4px_30px_rgba(0,0,0,0.3)] border-b border-gray-800/50' : 'bg-transparent'
    }`}>
      {/* Premium subtle accent line */}
      <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-teal-500/30 to-transparent"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo - using the provided FXentra logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/dashboard" className="flex items-center group">
              <div className="flex items-center gap-2">
                <div className="relative h-11 w-auto transform group-hover:scale-105 transition-all duration-500">
                  <Image
                    src="/images/fxentra-logo.png"
                    alt="FXentra Logo"
                    width={120}
                    height={44}
                    className="object-contain"
                  />
                </div>
              </div>
            </Link>
          </div>

          {/* Desktop nav - enhanced with premium styling */}
          <div className="hidden md:flex items-center space-x-6">
            <Link href="/dashboard" className="text-gray-200 hover:text-teal-400 px-3 py-2 text-sm font-medium tracking-wide transition-all duration-300 border-b border-transparent hover:border-teal-500 relative group">
              <span>DASHBOARD</span>
              <span className="absolute bottom-0 left-1/2 w-0 h-[1px] bg-gradient-to-r from-teal-500 to-teal-300 group-hover:w-full group-hover:left-0 transition-all duration-300"></span>
            </Link>
            <Link href="/place-order" className="text-gray-200 hover:text-teal-400 px-3 py-2 text-sm font-medium tracking-wide transition-all duration-300 border-b border-transparent hover:border-teal-500 relative group">
              <span>PLACE ORDER</span>
              <span className="absolute bottom-0 left-1/2 w-0 h-[1px] bg-gradient-to-r from-teal-500 to-teal-300 group-hover:w-full group-hover:left-0 transition-all duration-300"></span>
            </Link>
          </div>

          {/* User menu */}
          <div className="hidden md:flex items-center space-x-5">
            {username && (
              <div className="text-gray-300 px-3 py-2 text-sm font-medium">
                Welcome, {username}
              </div>
            )}
            <button
              onClick={handleLogout}
              className="relative px-8 py-3 rounded-full overflow-hidden group"
            >
              {/* Background gradient effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-teal-500 to-teal-400 transition-all duration-300"></div>

              {/* Animated shine effect */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              </div>

              {/* Button content */}
              <div className="relative flex items-center justify-center text-white text-sm font-medium tracking-wide">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-teal-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logout
              </div>
            </button>
          </div>

          {/* Mobile menu button - enhanced with subtle effects */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-teal-400 focus:outline-none transition-all duration-300"
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu - enhanced with premium styling */}
      {isMenuOpen && (
        <div className="md:hidden bg-gradient-to-b from-dark-light/95 to-dark/95 backdrop-blur-lg border-t border-gray-800/50 shadow-[0_10px_20px_rgba(0,0,0,0.3)]">
          <div className="px-3 pt-2 pb-4 space-y-1.5">
            <Link
              href="/dashboard"
              className="block text-gray-300 hover:text-teal-400 px-4 py-2.5 text-base font-medium border-l border-gray-700/30 hover:border-teal-500 transition-all duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
              DASHBOARD
            </Link>
            <Link
              href="/place-order"
              className="block text-gray-300 hover:text-teal-400 px-4 py-2.5 text-base font-medium border-l border-gray-700/30 hover:border-teal-500 transition-all duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
              PLACE ORDER
            </Link>
          </div>
          <div className="pt-4 pb-4 border-t border-gray-800/50">
            <div className="flex items-center px-5 space-y-3 flex-col">
              {username && (
                <div className="w-full text-gray-300 px-4 py-2.5 text-base font-medium">
                  Welcome, {username}
                </div>
              )}
              <button
                onClick={() => {
                  handleLogout();
                  setIsMenuOpen(false);
                }}
                className="w-full relative py-3 rounded-full overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-teal-500 to-teal-400 transition-all duration-300"></div>
                <div className="relative flex items-center justify-center text-white text-base font-medium">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Logout
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default DashboardNavbar;
