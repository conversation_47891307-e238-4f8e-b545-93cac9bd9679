'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
  index: number;
}

interface DashboardSidebarProps {
  onCollapse?: (collapsed: boolean) => void;
}

const NavItem = ({ href, icon, label, active, index }: NavItemProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <Link
        href={href}
        className={`group relative flex items-center space-x-3 px-4 py-3.5 rounded-xl transition-all duration-300 ${
          active
            ? 'bg-gradient-to-r from-teal-500/20 to-blue-500/10 text-teal-400 font-medium'
            : 'text-gray-400 hover:text-teal-400 hover:bg-gradient-to-r hover:from-teal-500/10 hover:to-transparent'
        }`}
      >
        {active && (
          <motion.div
            layoutId="activeIndicator"
            className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-teal-400 to-blue-400 rounded-full"
          />
        )}
        <div className={`flex items-center justify-center w-7 h-7 rounded-lg ${active ? 'bg-teal-500/20 text-teal-400' : 'text-gray-500 group-hover:text-teal-400'} transition-colors duration-300`}>
          {icon}
        </div>
        <span className={`font-medium ${active ? 'bg-gradient-to-r from-teal-300 to-blue-300 bg-clip-text text-transparent' : ''}`}>{label}</span>

        {active && (
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-teal-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
        )}
      </Link>
    </motion.div>
  );
};

export default function DashboardSidebar({ onCollapse }: DashboardSidebarProps) {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (onCollapse) {
      onCollapse(isCollapsed);
    }
  }, [isCollapsed, onCollapse]);

  const navItems = [
    {
      href: '/dashboard',
      label: 'Dashboard',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      )
    },
    {
      href: '/dashboard/place-order',
      label: 'Place Order',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      href: '/dashboard/kyc',
      label: 'KYC',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
        </svg>
      )
    },
    {
      href: '/dashboard/withdrawal',
      label: 'Withdrawal',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ];

  const sidebarVariants = {
    expanded: { width: '16rem' },
    collapsed: { width: '5rem' }
  };

  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <motion.div
      initial={false}
      animate={isCollapsed ? 'collapsed' : 'expanded'}
      variants={sidebarVariants}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="h-screen fixed top-0 left-0 bg-gradient-to-b from-[#0D1829] via-[#0A121F] to-[#0A1018] border-r border-gray-800/50 flex-shrink-0 z-10 overflow-hidden shadow-xl flex flex-col"
    >
      {/* Background elements */}
      <div className="absolute inset-0 z-0 pointer-events-none overflow-hidden">
        {/* Grid pattern with lower opacity */}
        <div className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'40\' height=\'40\' viewBox=\'0 0 40 40\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M0 20h20v20H0V20zm20 0h20v20H20V20zM0 0h20v20H0V0zm20 0h20v20H20V0z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '24px 24px'
          }}
        />

        {/* Ambient glow effects */}
        <div className="absolute top-1/4 -left-20 w-[200px] h-[200px] bg-teal-400/5 rounded-full blur-[80px] animate-pulse-slow opacity-50 mix-blend-screen"/>
        <div className="absolute bottom-1/4 -right-20 w-[200px] h-[200px] bg-blue-400/5 rounded-full blur-[80px] animate-pulse-slow-delay opacity-50 mix-blend-screen"/>
      </div>

      <div className="flex flex-col h-full relative z-10">
        {/* Logo and collapse toggle */}
        <motion.div
          className="flex items-center justify-between px-5 h-16 border-b border-gray-800/50 bg-gradient-to-r from-gray-900/90 to-transparent backdrop-blur-sm"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Link href="/dashboard" className="flex items-center">
            <motion.div
              className={`flex items-center gap-3 ${isCollapsed ? 'justify-center w-full' : ''}`}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <div className="relative group">
                <div className="absolute -inset-1 bg-gradient-to-r from-teal-500 to-blue-500 rounded-lg blur opacity-30 group-hover:opacity-70 transition duration-500"></div>
                <div className="relative">
                  <Image
                    src="/images/fxentra-logo.png"
                    alt="FXentra Logo"
                    width={isCollapsed ? 30 : 100}
                    height={isCollapsed ? 30 : 36}
                    className="object-contain"
                  />
                </div>
              </div>
            </motion.div>
          </Link>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleCollapsed}
            className="w-8 h-8 rounded-lg flex items-center justify-center text-gray-400 hover:text-teal-400 bg-gray-800/50 hover:bg-gray-800 focus:outline-none transition-colors duration-300"
          >
            {isCollapsed ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              </svg>
            )}
          </motion.button>
        </motion.div>

        {/* Navigation links */}
        <div className="flex-1 py-5 overflow-y-auto no-scrollbar">
          <div className={`px-3 space-y-1.5 ${!mounted ? 'opacity-0' : 'opacity-100 transition-opacity duration-500'}`}>
            {navItems.map((item, index) => (
              <div key={item.href} className={isCollapsed ? 'text-center' : ''}>
                {isCollapsed ? (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <Link
                      href={item.href}
                      className={`relative flex flex-col items-center py-3 px-1 rounded-xl transition-all duration-300 ${
                        pathname === item.href
                          ? 'bg-gradient-to-b from-teal-500/20 to-transparent text-teal-400'
                          : 'text-gray-500 hover:text-teal-400 hover:bg-gray-800/30'
                      }`}
                    >
                      {pathname === item.href && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full"
                        />
                      )}
                      <div className={`flex items-center justify-center w-8 h-8 rounded-lg mb-1 ${pathname === item.href ? 'bg-teal-500/20 text-teal-400' : 'hover:bg-gray-800/50'} transition-colors duration-300`}>
                        {item.icon}
                      </div>
                      <span className="text-xs font-medium">{item.label}</span>
                    </Link>
                  </motion.div>
                ) : (
                  <NavItem
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    active={pathname === item.href}
                    index={index}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* User profile section */}
        <motion.div
          className="border-t border-gray-800/50 p-4 bg-gradient-to-b from-transparent to-gray-900/50 backdrop-blur-sm"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <div className={`flex ${isCollapsed ? 'justify-center' : 'items-center space-x-3'}`}>
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full blur opacity-30 group-hover:opacity-70 transition duration-300"></div>
              <div className="relative w-9 h-9 rounded-full bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 flex items-center justify-center shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            {!isCollapsed && (
              <motion.div
                className="flex-1"
                initial={{ opacity: 0, x: -5 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                <div className="text-sm font-medium bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">John Doe</div>
                <button
                  className="text-xs text-gray-400 hover:text-red-400 transition-colors duration-300 flex items-center gap-1 mt-1"
                  onClick={() => {
                    // Add logout logic here
                    // Redirect to login page or handle logout
                  }}
                >
                  <span>Log Out</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </button>
              </motion.div>
            )}
            {isCollapsed && (
              <motion.button
                className="mt-2 text-xs text-gray-400 hover:text-red-400 transition-colors duration-300 p-2 rounded-lg bg-gray-800/50 hover:bg-gray-800"
                onClick={() => {
                  // Add logout logic here
                  // Redirect to login page or handle logout
                }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}