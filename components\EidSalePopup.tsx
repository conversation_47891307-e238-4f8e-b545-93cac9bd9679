'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface SalePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

// Account pricing with special discounts
const pricingOffers = [
  {
    size: '$1,000',
    originalPrice: 12.5,
    salePrice: 10,
    savings: 2.5,
    discount: '20%',
    popular: false
  },
  {
    size: '$3,000',
    originalPrice: 16.25,
    salePrice: 13,
    savings: 3.25,
    discount: '20%',
    popular: false
  },
  {
    size: '$5,000',
    originalPrice: 27.5,
    salePrice: 22,
    savings: 5.5,
    discount: '20%',
    popular: true
  },
  {
    size: '$10,000',
    originalPrice: 43.75,
    salePrice: 35,
    savings: 8.75,
    discount: '20%',
    popular: false
  },
  {
    size: '$25,000',
    originalPrice: 86.25,
    salePrice: 69,
    savings: 17.25,
    discount: '20%',
    popular: false
  },
  {
    size: '$50,000',
    originalPrice: 200,
    salePrice: 60,
    savings: 140,
    discount: '70%',
    popular: false
  },
  {
    size: '$100,000',
    originalPrice: 283.33,
    salePrice: 85,
    savings: 198.33,
    discount: '70%',
    popular: false
  },
  {
    size: '$200,000',
    originalPrice: 500,
    salePrice: 150,
    savings: 350,
    discount: '70%',
    popular: false
  }
];

const SalePopup: React.FC<SalePopupProps> = ({ isOpen, onClose }) => {
  const [selectedOffer, setSelectedOffer] = useState(pricingOffers[2]); // Default to $5,000
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Countdown timer for limited time offer
  useEffect(() => {
    const saleEndDate = new Date();
    saleEndDate.setDate(saleEndDate.getDate() + 7);

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = saleEndDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-md">
      <div className="relative w-full max-w-xl mx-auto bg-gradient-to-br from-[#0A1018] to-[#101C2C] rounded-2xl shadow-2xl border border-teal-500/20 p-0 sm:p-0 flex flex-col md:flex-row overflow-hidden animate-fadeIn">
        {/* Close button - top left, always visible */}
        <button
          onClick={onClose}
          className="absolute top-3 left-3 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-teal-500/10 hover:bg-teal-500/20 text-teal-300 hover:text-white focus:outline-none shadow-md transition-all duration-200"
          aria-label="Close popup"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

        {/* Main content grid: left = account sizes, right = offer details */}
        <div className="flex flex-col md:flex-row w-full">
          {/* Account Size Selection */}
          <div className="md:w-1/2 w-full bg-gradient-to-b from-teal-900/60 to-slate-800/40 p-4 flex flex-col justify-center">
            <div className="mb-2 text-center md:text-left">
              <div className="inline-block mb-2">
                <div className="bg-teal-500/20 rounded-full px-4 py-1 border border-teal-500/30 shadow-sm">
                  <span className="text-teal-200 text-xs font-semibold tracking-widest uppercase">Flash Sale • 60% Off</span>
                </div>
              </div>
              <h2 className="text-lg md:text-xl font-extrabold text-white mb-1 tracking-tight">Premium Trading Challenge</h2>
              <p className="text-xs text-teal-100/80 mb-2">Limited Time Offer</p>
            </div>
            <div className="grid grid-cols-4 gap-1 md:gap-2 mb-2">
                {pricingOffers.map((offer, index) => (
                  <div
                    key={index}
                    onClick={() => setSelectedOffer(offer)}
                    className={`relative cursor-pointer group`}
                  >
                  <div className={`p-2 text-center rounded-lg transition-all duration-200 border shadow-sm ${
                      selectedOffer.size === offer.size
                      ? 'bg-gradient-to-b from-teal-500/30 to-teal-500/10 border-teal-400/40 shadow-teal-500/10 scale-105'
                      : 'bg-white/5 hover:bg-teal-500/10 border-transparent hover:border-teal-500/10'
                    }`}>
                      {offer.popular && (
                      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-teal-500 text-white text-[9px] px-1.5 py-0.5 rounded-full shadow">POPULAR</div>
                      )}
                    <div className="font-semibold text-xs text-white mb-0.5">{offer.size.replace('$', '').replace(',', '')}K</div>
                    <div className={`text-xs font-bold ${selectedOffer.size === offer.size ? 'text-teal-300' : 'text-white/80'}`}>${offer.salePrice}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

          {/* Offer Details and Actions */}
          <div className="md:w-1/2 w-full flex flex-col justify-center p-4">
            {/* Countdown Timer */}
            <div className="flex justify-center gap-2 mb-2">
              {Object.entries(timeLeft).map(([unit, value]) => (
                <div key={unit} className="text-center">
                  <div className="bg-black/30 rounded-md px-2 py-1 border border-teal-500/10 shadow-sm">
                    <div className="text-lg font-bold text-white mb-0.5">{value}</div>
                    <div className="text-[10px] text-teal-200/60 uppercase tracking-wider">{unit}</div>
                  </div>
                </div>
              ))}
            </div>
            {/* Offer Details */}
            <div className="bg-gradient-to-b from-teal-900/60 to-slate-800/40 rounded-lg p-3 border border-teal-500/10 mb-2 shadow-inner">
              <div className="text-center">
                <h4 className="text-base font-bold text-white mb-1">{selectedOffer.size} Account</h4>
                <div className="flex justify-center items-center space-x-2 mb-1">
                  <div className="text-xs text-white/40 line-through font-medium">${selectedOffer.originalPrice}</div>
                  <div className="px-1.5 py-0.5 bg-teal-500/20 rounded border border-teal-500/30">
                    <span className="text-teal-400 font-bold text-xs">SAVE {selectedOffer.discount}</span>
                  </div>
                  <div className="text-lg font-bold text-white">${selectedOffer.salePrice}</div>
                </div>
                <div className="text-white/60 text-xs">Total Savings: <span className="text-teal-400 font-semibold">${selectedOffer.savings}</span></div>
              </div>
            </div>
            {/* Features */}
            <div className="grid grid-cols-2 gap-2 mb-2">
              <div className="flex items-center space-x-1 text-xs">
                <svg className="w-3 h-3 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                  <span className="text-white/80">2-Phase Evaluation</span>
                </div>
              <div className="flex items-center space-x-1 text-xs">
                <svg className="w-3 h-3 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                  <span className="text-white/80">90% Profit Share</span>
                </div>
              <div className="flex items-center space-x-1 text-xs">
                <svg className="w-3 h-3 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                  <span className="text-white/80">Pro Trading Tools</span>
                </div>
              <div className="flex items-center space-x-1 text-xs">
                <svg className="w-3 h-3 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" /></svg>
                  <span className="text-white/80">Instant Access</span>
              </div>
            </div>
            {/* CTA Buttons */}
            <div className="flex gap-2 mt-2">
              <Link
                href={`/place-order?type=twoStep&size=${selectedOffer.size.replace('$', '').replace(',', '')}&special_offer=true`}
                className="flex-1 px-3 py-2 bg-gradient-to-r from-teal-500 to-emerald-400 hover:from-teal-400 hover:to-emerald-300 text-white font-semibold rounded-lg shadow-md shadow-teal-500/20 hover:shadow-teal-500/40 transition-all duration-200 text-center text-xs tracking-wide border-0"
                onClick={onClose}
              >
                Get Started
              </Link>
              <button
                onClick={onClose}
                className="px-3 py-2 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white font-medium rounded-lg border border-white/10 hover:border-white/20 transition-all duration-200 text-xs"
              >
                Maybe Later
              </button>
            </div>
            {/* Footer */}
            <div className="mt-2 text-center">
              <div className="flex justify-center items-center gap-3 text-white/40 text-[10px] mb-1">
                <div className="flex items-center gap-1.5">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4h8z" /></svg>
                  <span>Secure Payment</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>
                  <span>Instant Access</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" /></svg>
                  <span>24/7 Support</span>
                </div>
              </div>
              <button
                onClick={() => {
                  localStorage.setItem('sale-popup-dismissed', 'true');
                  onClose();
                }}
                className="text-white/40 hover:text-white/60 text-[10px] transition-colors"
              >
                Don't show this offer again
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalePopup;
