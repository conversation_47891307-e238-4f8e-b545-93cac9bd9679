'use client';

import Link from 'next/link';
import { useEffect, useState, useRef } from 'react';
import TradingChart from './TradingChart';

// Countdown timer component with luxury styling
const CountdownTimer = () => {
  // Set timer for 5 days from first visit
  const [time, setTime] = useState({
    days: 5,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    // Get or set the deadline in localStorage
    let deadline = localStorage.getItem('offerDeadline');
    if (!deadline) {
      const newDeadline = new Date();
      newDeadline.setDate(newDeadline.getDate() + 5); // Set to 5 days from now
      deadline = newDeadline.getTime().toString();
      localStorage.setItem('offerDeadline', deadline);
    }

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = parseInt(deadline || '0') - now;

      // If countdown is finished, reset for another 5 days
      if (distance < 0) {
        const newDeadline = new Date();
        newDeadline.setDate(newDeadline.getDate() + 5);
        const newDeadlineStr = newDeadline.getTime().toString();
        localStorage.setItem('offerDeadline', newDeadlineStr);
        deadline = newDeadlineStr;
      }

      setTime({
        days: Math.floor(distance / (1000 * 60 * 60 * 24)),
        hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((distance % (1000 * 60)) / 1000)
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const timeBoxStyle = "flex-1 bg-gradient-to-b from-gray-800/80 to-gray-900/90 backdrop-blur-lg rounded-xl flex flex-col items-center justify-center border border-teal-500/20 min-w-[60px] sm:min-w-[80px] p-3 sm:p-5 shadow-xl shadow-teal-900/20";
  const numberStyle = "text-2xl sm:text-3xl md:text-5xl font-bold bg-gradient-to-r from-teal-300 to-teal-400 bg-clip-text text-transparent";
  const labelStyle = "text-[10px] sm:text-xs uppercase tracking-wider text-gray-400 mt-1 sm:mt-2 font-medium";

  return (
    <div className="w-full max-w-2xl mx-auto mt-6 sm:mt-10 px-4">
      <div className="relative">
        {/* Animated glow effect */}
        <div className="absolute inset-0 bg-teal-500/5 rounded-3xl blur-2xl animate-pulse"></div>
        
        {/* Glass card */}
        <div className="relative bg-gradient-to-b from-gray-900/80 to-gray-950/95 backdrop-blur-xl rounded-2xl p-4 sm:p-8 border border-gray-800/50">
          <div className="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-blue-500/5 rounded-2xl overflow-hidden">
            <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]"></div>
          </div>
          
          <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-center mb-4 sm:mb-6">
            <span className="bg-gradient-to-r from-teal-300 to-teal-400 bg-clip-text text-transparent">
              EXCLUSIVE LAUNCH OFFER
            </span>
          </h3>
          
          <div className="flex gap-2 sm:gap-4 md:gap-6 relative z-10">
            <div className={timeBoxStyle}>
              <span className={numberStyle}>{time.days}</span>
              <span className={labelStyle}>Days</span>
            </div>
            <div className={timeBoxStyle}>
              <span className={numberStyle}>{time.hours}</span>
              <span className={labelStyle}>Hours</span>
            </div>
            <div className={timeBoxStyle}>
              <span className={numberStyle}>{time.minutes}</span>
              <span className={labelStyle}>Minutes</span>
            </div>
            <div className={timeBoxStyle}>
              <span className={numberStyle}>{time.seconds}</span>
              <span className={labelStyle}>Seconds</span>
            </div>
          </div>
          
          <div className="text-center mt-4 sm:mt-6 text-gray-400 text-xs sm:text-sm font-medium">
            Don't miss out on this limited-time opportunity
          </div>
        </div>
      </div>
    </div>
  );
};

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const heroRef = useRef(null);

  // Handle animations on load
  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section 
      ref={heroRef} 
      className="relative min-h-screen pt-16 sm:pt-20 flex flex-col items-center justify-center overflow-hidden"
    >
      {/* Premium background elements */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Dark gradient base */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-950 via-dark to-gray-950"></div>
        
        {/* Subtle grid pattern */}
        <div 
          className="absolute inset-0 opacity-[0.03]" 
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '30px 30px'
          }}
        />
        
        {/* Luxury radial gradient */}
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-[radial-gradient(ellipse_at_center,rgba(14,165,233,0.05),transparent_50%)]"></div>
        
        {/* Animated floating shapes */}
        <div className="absolute top-20 right-[20%] w-64 h-64 rounded-full bg-gradient-to-br from-teal-500/10 to-blue-500/5 blur-3xl animate-float-slow"></div>
        <div className="absolute top-[40%] left-[10%] w-80 h-80 rounded-full bg-gradient-to-tr from-indigo-500/5 to-teal-500/10 blur-3xl animate-float-slow-reverse"></div>
        <div className="absolute bottom-40 right-[15%] w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 to-purple-500/5 blur-3xl animate-pulse"></div>
        
        {/* Sophisticated light traces */}
        <div className="absolute h-[1px] w-full top-[20%] left-0 bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
        <div className="absolute h-[1px] w-full bottom-[30%] left-0 bg-gradient-to-r from-transparent via-teal-500/10 to-transparent"></div>
        <div className="absolute w-[1px] h-full top-0 left-[25%] bg-gradient-to-b from-transparent via-teal-500/10 to-transparent"></div>
        <div className="absolute w-[1px] h-full top-0 right-[25%] bg-gradient-to-b from-transparent via-teal-500/10 to-transparent"></div>
      </div>

      {/* Content container */}
      <div className="relative z-10 container mx-auto px-4 py-8 sm:py-16 flex flex-col items-center">
        <div 
          className={`text-center max-w-5xl mx-auto transition-all duration-1000 ease-out transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
        >
          {/* Elite badge */}
          <div className="inline-block mb-6 sm:mb-8 rounded-full bg-gradient-to-r from-gray-800 to-gray-900 px-4 sm:px-6 py-2 border border-gray-700 shadow-lg">
            <span className="bg-gradient-to-r from-teal-300 to-teal-400 bg-clip-text text-transparent text-xs sm:text-sm font-semibold tracking-wider">ELITE TRADER FUNDING</span>
          </div>
          
          {/* Main Heading */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight tracking-tight px-2">
            <span className="block mb-2 sm:mb-4">Capital Without Constraints</span>
            <span className="relative">
              <span className="relative z-10 text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-teal-400">Unlock Your Trading Potential</span>
              <span className="absolute -bottom-1 left-0 right-0 h-[6px] bg-gradient-to-r from-teal-400/30 to-teal-300/0 blur-sm"></span>
            </span>
          </h1>
          
          {/* Luxury tagline */}
          <p className="mt-6 sm:mt-8 text-lg sm:text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed font-light px-4">
            Access up to <span className="font-semibold text-white">$200,000</span> in trading capital with our exclusive fast-track program.
            <span className="block mt-2 sm:mt-3 text-teal-300 font-medium">Superior execution. Institutional-grade tools. Rapid scaling.</span>
          </p>
          
          {/* Prestige highlights */}
          <div className="mt-8 sm:mt-10 grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-3xl mx-auto px-4">
            <div className="flex flex-col items-center backdrop-blur-sm bg-gray-900/30 rounded-xl p-4 border border-gray-800/50">
              <span className="text-xl sm:text-2xl md:text-3xl font-bold text-white">90%</span>
              <span className="text-gray-400 text-sm mt-1">Profit Share</span>
            </div>
            <div className="flex flex-col items-center backdrop-blur-sm bg-gray-900/30 rounded-xl p-4 border border-gray-800/50">
              <span className="text-xl sm:text-2xl md:text-3xl font-bold text-white">Instant</span>
              <span className="text-gray-400 text-sm mt-1">Account Access</span>
            </div>
            <div className="flex flex-col items-center backdrop-blur-sm bg-gray-900/30 rounded-xl p-4 border border-gray-800/50">
              <span className="text-xl sm:text-2xl md:text-3xl font-bold text-white">Pro</span>
              <span className="text-gray-400 text-sm mt-1">Trading Platform</span>
            </div>
          </div>
          
          {/* Countdown timer */}
          <CountdownTimer />
          
          {/* Trading Chart Section */}
          <div className="mt-12 w-full max-w-5xl mx-auto px-4">
            <TradingChart />
          </div>
          
          {/* Call to Actions */}
          <div className="mt-8 sm:mt-10 flex flex-col sm:flex-row items-stretch sm:items-center justify-center gap-4 sm:gap-12 px-4">
            <Link 
              href="/signup" 
              className="group relative w-full sm:w-auto px-6 sm:px-8 py-4 sm:py-5 text-base font-semibold rounded-lg text-gray-900 overflow-hidden transition-all duration-300"
            >
              {/* Button background with animated gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-teal-400 to-teal-300 group-hover:from-teal-300 group-hover:to-teal-400 transition-all duration-500"></div>
              
              {/* Glass reflection effect */}
              <div className="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-300 bg-[linear-gradient(45deg,rgba(255,255,255,0)_25%,rgba(255,255,255,0.3)_50%,rgba(255,255,255,0)_75%)] bg-[length:250%_250%] animate-shimmer"></div>
              
              {/* Shadow effect */}
              <div className="absolute -inset-1 rounded-lg opacity-30 bg-gradient-to-r from-teal-400 to-teal-300 blur group-hover:opacity-40 transition-opacity duration-300"></div>
              
              {/* Button text */}
              <span className="relative flex items-center justify-center">
                <span>Reserve Your Capital</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </span>
            </Link>
            <Link 
              href="#how-it-works" 
              className="group relative w-full sm:w-auto px-6 sm:px-8 py-4 sm:py-5 text-base font-semibold rounded-lg overflow-hidden transition-all duration-300 text-center sm:text-left"
            >
              {/* Button background with subtle glow */}
              <div className="absolute inset-0 border border-teal-700/30 bg-gray-900/80 backdrop-blur-md group-hover:bg-gray-800/90 transition-all duration-300 rounded-lg"></div>
              <div className="absolute -inset-0.5 bg-teal-500/10 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              {/* Button text */}
              <span className="relative flex items-center justify-center text-white group-hover:text-teal-200 transition-colors duration-300">
                <span>Explore Features</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-y-1 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection; 
