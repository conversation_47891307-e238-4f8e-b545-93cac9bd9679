const steps = [
  {
    id: 1,
    title: 'Choose a Challenge',
    description: 'Select the account size and challenge that matches your trading goals and experience level.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
    ),
  },
  {
    id: 2,
    title: 'Pass the Challenge',
    description: 'Trade within our risk parameters and meet the profit target within the specified timeframe.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: 3,
    title: 'Get Funded',
    description: 'Once you pass, we\'ll provide you with a funded account where you can trade with our capital.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: 4,
    title: 'Earn Profits',
    description: 'Keep up to 90% of the profits you generate, with payouts available on a bi-weekly basis.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
      </svg>
    ),
  },
];

const HowItWorksSection = () => {
  return (
    <section id="howItWorks" className="py-32 bg-[#030609] relative overflow-hidden">
      {/* Enhanced background elements */}
      <div className="absolute inset-0 z-0">
        {/* Refined gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        
        {/* Enhanced geometric elements */}
        <div className="absolute top-0 left-0 w-full h-full opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#4FD1C5" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#2D3748" stopOpacity="0.4" />
              </linearGradient>
              <pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse">
                <path d="M 8 0 L 0 0 0 8" fill="none" stroke="url(#grid-gradient)" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect x="0" y="0" width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        
        {/* Enhanced glowing orbs */}
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="inline-block text-base font-semibold text-teal-400 tracking-wider uppercase bg-teal-400/10 px-4 py-1 rounded-full mb-3">HOW IT WORKS</h2>
          <p className="mt-3 text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl drop-shadow-md bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-200">
            Your Path to Funded Trading
          </p>
          <p className="mt-6 text-xl text-gray-300 mx-auto leading-relaxed">
            Our simple 4-step process takes you from challenge to funded trader with up to $200,000 in trading capital.
          </p>
        </div>

        <div className="mt-24">
          <div className="relative">
            {/* Enhanced connection line */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="hidden sm:block w-full h-0.5 bg-gradient-to-r from-transparent via-teal-400/20 to-transparent"></div>
            </div>
            
            {/* Enhanced steps */}
            <div className="relative flex justify-between items-center flex-col sm:flex-row gap-16 sm:gap-8">
              {steps.map((step, idx) => (
                <div 
                  key={step.id} 
                  className="flex-1 text-center relative group"
                >
                  <div className="relative flex flex-col items-center">
                    {/* Enhanced glowing effect behind icon */}
                    <div className="absolute inset-0 rounded-full bg-teal-400/10 blur-2xl w-20 h-20 mx-auto transform group-hover:scale-110 transition-transform duration-500"></div>
                    
                    {/* Enhanced icon container */}
                    <div className="rounded-2xl border border-teal-400/30 bg-[#030609]/80 backdrop-blur-xl p-6 relative z-10 shadow-xl shadow-teal-400/10 transition-all duration-500 group-hover:shadow-teal-400/20 group-hover:scale-105 group-hover:border-teal-400/50">
                      <div className="relative">
                        {step.icon}
                        {/* Subtle pulse animation */}
                        <div className="absolute inset-0 rounded-full bg-teal-400/20 animate-ping opacity-20"></div>
                      </div>
                    </div>
                    
                    {/* Enhanced step number */}
                    <div className="absolute -top-3 -right-3 w-8 h-8 rounded-full bg-gradient-to-r from-teal-400 to-teal-500 flex items-center justify-center text-sm font-bold text-gray-900 z-20 shadow-lg border border-teal-300/20 transform group-hover:scale-110 transition-transform duration-500">
                      {step.id}
                    </div>
                    
                    <h3 className="mt-8 text-2xl font-bold text-white group-hover:text-teal-300 transition-colors duration-300">
                      {step.title}
                    </h3>
                    <p className="mt-4 text-base text-gray-300/90 max-w-xs mx-auto leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-24 text-center">
          <p className="text-lg text-gray-300/90 mb-8 font-medium">
            Ready to start your funded trading journey?
          </p>
          <a
            href="#pricing"
            className="inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-xl text-gray-900 bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 shadow-lg shadow-teal-400/20 hover:shadow-teal-400/30 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 md:text-lg relative overflow-hidden group"
          >
            <span className="relative z-10">View Challenges</span>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
          </a>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection; 