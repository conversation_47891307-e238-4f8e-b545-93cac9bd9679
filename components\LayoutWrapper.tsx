'use client';

import { usePathname } from 'next/navigation';
import Navbar from './Navbar';
import Footer from './Footer';
import SaleBanner from '../app/components/SaleBanner';

export default function LayoutWrapper({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  const pathname = usePathname();
  const isDashboard = pathname?.startsWith('/dashboard');
  const isAdmin = pathname?.startsWith('/admin');
  
  return (
    <>
      {!isDashboard && !isAdmin && (
        <div className="fixed top-0 left-0 right-0 z-50">
          <SaleBanner />
        </div>
      )}
      <div className="relative flex flex-col min-h-screen w-full">
        {!isDashboard && !isAdmin && (
          <>
            <Navbar />
            <div className="h-[108px]" /> {/* Combined height of banner (44px) + navbar (64px) */}
          </>
        )}
        <main className="flex-grow w-full">{children}</main>
        {!isDashboard && !isAdmin && <Footer />}
      </div>
    </>
  );
} 