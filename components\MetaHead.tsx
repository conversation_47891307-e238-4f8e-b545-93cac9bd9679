interface MetaHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  canonical?: string;
}

export default function MetaHead({
  title = 'New Firm - Professional Prop Trading Funding',
  description = 'Get funded with up to $200,000 for trading forex, crypto, and stocks. New Firm offers up to 90% profit sharing with professional prop trading accounts.',
  keywords = 'prop firm, funded trading, forex, crypto, stocks, trading challenge, prop trading, funded account',
  ogImage = '/images/og-image.jpg',
  ogUrl = 'https://newfirm.com',
  canonical,
}: MetaHeadProps) {
  // Base URL for absolute URLs
  const baseUrl = 'https://newfirm.com';
  
  // Determine canonical URL
  const canonicalUrl = canonical ? `${baseUrl}${canonical}` : ogUrl;
  
  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      images: [
        {
          url: `${baseUrl}${ogImage}`,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      type: 'website',
      siteName: 'New Firm',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}${ogImage}`],
    },
    alternates: {
      canonical: canonicalUrl,
    },
  };
} 