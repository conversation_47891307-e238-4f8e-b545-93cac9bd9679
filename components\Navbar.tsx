'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import Image from 'next/image';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [tradingDropdownOpen, setTradingDropdownOpen] = useState(false);
  const [aboutDropdownOpen, setAboutDropdownOpen] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        setTradingDropdownOpen(false);
        setAboutDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <nav className={`fixed top-[44px] w-full z-40 transition-all duration-300 backdrop-blur-md bg-dark/80 border-b border-teal-500/10 shadow-lg rounded-b-2xl ${
      scrolled ? 'shadow-[0_4px_30px_rgba(0,0,0,0.3)] border-b border-teal-500/20' : ''
    }`}>
      {/* Premium subtle accent line */}
      <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-teal-500/40 to-transparent"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo - perfectly centered vertically and horizontally in its section */}
          <div className="flex items-center h-full">
            <Link href="/" className="flex items-center group h-full">
              <div className="flex items-center gap-2 h-full">
                <div className="relative h-11 w-auto flex items-center justify-center group-hover:scale-105 transition-all duration-500">
                  <Image
                    src="/images/fxentra-logo.png"
                    alt="FXentra Logo"
                    width={120}
                    height={44}
                    className="object-contain"
                  />
                </div>
              </div>
            </Link>
          </div>

          {/* Desktop nav - premium, consistent color and spacing, more professional look */}
          <div className="hidden md:flex items-center space-x-8">
            {[
              { href: '/pricing', label: 'PRICING' },
              { href: '/faqs', label: 'FAQ' },
              { href: '#howItWorks', label: 'ROADMAP' },
            ].map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="relative px-3 py-2 text-sm font-semibold uppercase tracking-wider text-gray-200 transition-colors duration-200 hover:text-teal-400 focus:text-teal-400"
              >
                <span>{item.label}</span>
                <span className="absolute left-0 bottom-1 h-[2px] w-0 bg-gradient-to-r from-teal-400 to-teal-300 rounded-full transition-all duration-300 group-hover:w-full group-focus:w-full"></span>
              </Link>
            ))}

            {/* Trading Dropdown */}
            <div className="dropdown-container relative">
              <button
                onClick={() => setTradingDropdownOpen(!tradingDropdownOpen)}
                className="relative px-3 py-2 text-sm font-semibold uppercase tracking-wider text-gray-200 transition-colors duration-200 hover:text-teal-400 focus:text-teal-400 flex items-center"
              >
                <span>TRADING</span>
                <svg className={`ml-1 w-4 h-4 transition-transform duration-200 ${tradingDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
                <span className="absolute left-0 bottom-1 h-[2px] w-0 bg-gradient-to-r from-teal-400 to-teal-300 rounded-full transition-all duration-300 group-hover:w-full group-focus:w-full"></span>
              </button>
              
              {tradingDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-[#101C2C] border border-gray-700/50 rounded-xl shadow-xl backdrop-blur-md z-50">
                  <div className="py-2">
                    <Link
                      href="/symbols"
                      className="block px-4 py-3 text-sm text-gray-300 hover:text-teal-400 hover:bg-[#131B29]/50 transition-colors duration-200"
                      onClick={() => setTradingDropdownOpen(false)}
                    >
                      Symbols
                    </Link>
                    <Link
                      href="/economic-calendar"
                      className="block px-4 py-3 text-sm text-gray-300 hover:text-blue-400 hover:bg-[#131B29]/50 transition-colors duration-200"
                      onClick={() => setTradingDropdownOpen(false)}
                    >
                      Calendar
                    </Link>
                    <Link
                      href="/trading-platforms"
                      className="block px-4 py-3 text-sm text-gray-300 hover:text-teal-400 hover:bg-[#131B29]/50 transition-colors duration-200"
                      onClick={() => setTradingDropdownOpen(false)}
                    >
                      Trading Platforms
                    </Link>
                    <Link
                      href="/scaling-plan"
                      className="block px-4 py-3 text-sm text-gray-300 hover:text-teal-400 hover:bg-[#131B29]/50 transition-colors duration-200"
                      onClick={() => setTradingDropdownOpen(false)}
                    >
                      Scaling Plan
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* About Us Dropdown */}
            <div className="dropdown-container relative">
              <button
                onClick={() => setAboutDropdownOpen(!aboutDropdownOpen)}
                className="relative px-3 py-2 text-sm font-semibold uppercase tracking-wider text-gray-200 transition-colors duration-200 hover:text-teal-400 focus:text-teal-400 flex items-center"
              >
                <span>ABOUT US</span>
                <svg className={`ml-1 w-4 h-4 transition-transform duration-200 ${aboutDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
                <span className="absolute left-0 bottom-1 h-[2px] w-0 bg-gradient-to-r from-teal-400 to-teal-300 rounded-full transition-all duration-300 group-hover:w-full group-focus:w-full"></span>
              </button>
              
              {aboutDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-[#101C2C] border border-gray-700/50 rounded-xl shadow-xl backdrop-blur-md z-50">
                  <div className="py-2">
                    <Link
                      href="/social-media"
                      className="block px-4 py-3 text-sm text-gray-300 hover:text-teal-400 hover:bg-[#131B29]/50 transition-colors duration-200"
                      onClick={() => setAboutDropdownOpen(false)}
                    >
                      Social Media
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* CTA Buttons - luxury, consistent color, more realistic and balanced size */}
          <div className="hidden md:flex items-center space-x-4">
            <Link
              href="/signup"
              className="text-gray-200 hover:text-white px-5 py-1.5 text-sm font-semibold rounded-full border border-teal-500/30 bg-gradient-to-r from-teal-500/20 to-teal-400/10 hover:from-teal-500/30 hover:to-teal-400/20 shadow-sm transition-all duration-200"
            >
              Sign Up
            </Link>
            <Link
              href="/login"
              className="px-6 py-1.5 text-sm font-semibold rounded-full border border-teal-500/30 bg-gradient-to-r from-teal-500/30 to-teal-400/20 hover:from-teal-500/40 hover:to-teal-400/30 text-white shadow-sm flex items-center gap-2 transition-all duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-teal-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
              Login
            </Link>
          </div>

          {/* Mobile menu button - premium, consistent color */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-teal-400 focus:outline-none transition-all duration-300"
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu - premium, consistent color */}
      {isMenuOpen && (
        <div className="md:hidden bg-gradient-to-b from-dark-light/95 to-dark/95 backdrop-blur-lg border-t border-gray-800/50 shadow-xl rounded-b-2xl">
          <div className="px-3 pt-2 pb-4 space-y-1.5">
            <Link
              href="/pricing"
              className="block text-gray-300 hover:text-teal-400 px-4 py-2.5 text-base font-semibold border-l-4 border-transparent hover:border-teal-400 transition-all duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
              PRICING
            </Link>
            <Link
              href="/faqs"
              className="block text-gray-300 hover:text-teal-400 px-4 py-2.5 text-base font-semibold border-l-4 border-transparent hover:border-teal-400 transition-all duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
              FAQ
            </Link>
            <Link
              href="#howItWorks"
              className="block text-gray-300 hover:text-teal-400 px-4 py-2.5 text-base font-semibold border-l-4 border-transparent hover:border-teal-400 transition-all duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
              ROADMAP
            </Link>
            
            {/* Mobile Trading Submenu */}
            <div className="border-l-4 border-transparent px-4 py-2.5">
              <div className="text-gray-400 text-base font-semibold mb-2">TRADING</div>
              <div className="pl-4 space-y-1">
                <Link
                  href="/symbols"
                  className="block text-gray-300 hover:text-teal-400 py-2 text-sm transition-all duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Symbols
            </Link>
            <Link
                  href="/economic-calendar"
                  className="block text-gray-300 hover:text-blue-400 py-2 text-sm transition-all duration-300"
              onClick={() => setIsMenuOpen(false)}
            >
                  Calendar
            </Link>
                <Link
                  href="/trading-platforms"
                  className="block text-gray-300 hover:text-teal-400 py-2 text-sm transition-all duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Trading Platforms
                </Link>
                <Link
                  href="/scaling-plan"
                  className="block text-gray-300 hover:text-teal-400 py-2 text-sm transition-all duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Scaling Plan
                </Link>
              </div>
            </div>
            
            {/* Mobile About Us Submenu */}
            <div className="border-l-4 border-transparent px-4 py-2.5">
              <div className="text-gray-400 text-base font-semibold mb-2">ABOUT US</div>
              <div className="pl-4 space-y-1">
            <Link
              href="/social-media"
                  className="block text-gray-300 hover:text-teal-400 py-2 text-sm transition-all duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Social Media
                </Link>
              </div>
            </div>
          </div>
          <div className="pt-4 pb-4 border-t border-gray-800/50">
            <div className="flex items-center px-5 space-y-3 flex-col">
              <Link
                href="/signup"
                className="w-full text-base py-2.5 relative flex items-center justify-center group rounded-full border border-teal-500/40 bg-teal-500/10 hover:bg-teal-500/20 transition-all duration-300 shadow-sm"
                onClick={() => setIsMenuOpen(false)}
              >
                <span className="text-gray-200 group-hover:text-white relative z-10">Sign Up</span>
              </Link>
              <Link
                href="/login"
                className="w-full relative py-3 rounded-full overflow-hidden group border border-teal-500/40 bg-gradient-to-r from-teal-500/20 to-teal-400/20 hover:bg-gradient-to-r hover:from-teal-500/30 hover:to-teal-400/30 transition-all duration-300 shadow-sm"
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="relative flex items-center justify-center text-white text-base font-semibold">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-teal-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                Login
                </div>
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;