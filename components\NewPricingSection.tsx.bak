'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';

// Account type configuration
type AccountTypeKey = 'instant' | 'oneStep' | 'twoStep';
type AccountSizeKey = 1000 | 3000 | 5000 | 10000 | 25000 | 50000 | 100000 | 200000;

interface AccountTypeInfo {
  name: string;
  description: string;
  phases: number;
  phasesLabel: string;
  targetMultiplier: number;
  maxCapital: number;
}

// Account types
const accountTypes: Record<AccountTypeKey, AccountTypeInfo> = {
  instant: {
    name: 'Instant',
    description: 'Start trading immediately with a funded account after completion of purchase.',
    phases: 0,
    phasesLabel: 'No evaluation',
    targetMultiplier: 10,
    maxCapital: 50000
  },
  oneStep: {
    name: '1-Step',
    description: 'Complete one evaluation to prove your trading skills before receiving a funded account.',
    phases: 1,
    phasesLabel: 'One-phase evaluation',
    targetMultiplier: 8,
    maxCapital: 100000
  },
  twoStep: {
    name: '2-Step',
    description: 'Complete a two-phase evaluation process before receiving a funded account.',
    phases: 2,
    phasesLabel: 'Two-phase evaluation',
    targetMultiplier: 6,
    maxCapital: 200000
  }
};

// Account sizes
const accountSizes = [
  { id: 1, value: 1000, label: "1k", discountPercent: 20 },
  { id: 2, value: 3000, label: "3k", discountPercent: 20 },
  { id: 3, value: 5000, label: "5k", discountPercent: 20 },
  { id: 4, value: 10000, label: "10k", discountPercent: 20 },
  { id: 5, value: 25000, label: "25k", discountPercent: 20 },
  { id: 6, value: 50000, label: "50k", discountPercent: 50 },
  { id: 7, value: 100000, label: "100k", discountPercent: 50 },
  { id: 8, value: 200000, label: "200k", discountPercent: 50 },
];

// Pricing structure
const pricingStructure: Record<AccountTypeKey, Record<AccountSizeKey, number>> = {
  instant: {
    1000: 49,
    3000: 69,
    5000: 79,
    10000: 149,
    25000: 199,
    50000: 349,
    100000: 599,
    200000: 0 // Not available for instant
  },
  oneStep: {
    1000: 29,
    3000: 45,
    5000: 59,
    10000: 99,
    25000: 139,
    50000: 259,
    100000: 399,
    200000: 699
  },
  twoStep: {
    1000: 19,
    3000: 35,
    5000: 47,
    10000: 77,
    25000: 97,
    50000: 197,
    100000: 297,
    200000: 497
  }
};

// Price calculation based on account size and type
const calculatePrice = (accountType: AccountTypeKey, accountSize: AccountSizeKey): number => {
  // Ensure the account type and size exist in our pricing structure
  if (pricingStructure[accountType] && pricingStructure[accountType][accountSize]) {
    return pricingStructure[accountType][accountSize];
  }
  // Default fallback
  return 0;
};

const NewPricingSection = () => {
  const [accountType, setAccountType] = useState<AccountTypeKey>('instant');
  const [accountSize, setAccountSize] = useState<number>(5000);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  // Update account size when switching account types to ensure valid selection
  useEffect(() => {
    if (accountType === 'instant' && (accountSize === 1000 || accountSize === 3000)) {
      setAccountSize(5000);
    }
  }, [accountType, accountSize]);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // Calculate price and savings
  const price = calculatePrice(accountType, accountSize as AccountSizeKey);
  const originalPrice = Math.round(price * 1.25); // 20% discount
  const savings = originalPrice - price;

  return (
    <section 
      id="pricing" 
      ref={sectionRef}
      className={`py-24 relative ${isVisible ? 'animate-fadeIn' : 'opacity-0'}`}
    >
      {/* Decorative elements */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Premium texture overlay */}
        <div 
          className="absolute inset-0 opacity-10" 
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill-rule=\'evenodd\'%3E%3Cg fill=\'%239C92AC\' fill-opacity=\'0.3\'%3E%3Cpath opacity=\'.5\' d=\'M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '150px 150px'
          }}
        />
        
        {/* Enhanced geometric shapes */}
        <div className="absolute -top-[400px] -right-[400px] w-[800px] h-[800px] rounded-full bg-gradient-to-br from-teal-500/10 to-purple-500/10 blur-[120px]"></div>
        <div className="absolute -bottom-[300px] -left-[300px] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-blue-500/10 to-teal-500/10 blur-[120px]"></div>
        
        {/* Gold accent replaced with teal */}
        <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[300px] h-[3px] bg-gradient-to-r from-transparent via-teal-400/70 to-transparent"></div>
        <div className="absolute bottom-1/4 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-[300px] h-[3px] bg-gradient-to-r from-transparent via-teal-400/70 to-transparent"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section heading with more premium styling */}
        <div className="text-center mb-20">
          <div className="inline-block relative mb-2">
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 w-[50px] h-[50px] opacity-20">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-teal-400">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M14.5 8.5C14.5 9.88071 13.3807 11 12 11C10.6193 11 9.5 9.88071 9.5 8.5C9.5 7.11929 10.6193 6 12 6C13.3807 6 14.5 7.11929 14.5 8.5Z" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M6 19V18C6 15.7909 7.79086 14 10 14H14C16.2091 14 18 15.7909 18 18V19" stroke="currentColor" strokeWidth="0.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3 className="text-teal-400 text-sm uppercase tracking-[0.3em] font-semibold">FXentra Trading Solutions</h3>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8">
            <span className="bg-gradient-to-r from-teal-400 via-emerald-300 to-teal-500 text-transparent bg-clip-text">FXentra</span> Trading Challenges
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-teal-500 to-emerald-400 mx-auto mb-8"></div>
          <p className="text-gray-300 max-w-2xl mx-auto text-lg leading-relaxed">
            Select your FXentra account type and size that aligns with your trading strategy and goals.
            <span className="block text-teal-400 mt-2 font-medium">One-time payment, exclusive FXentra benefits.</span>
          </p>
        </div>

        {/* Trading Challenge Details */}
        <div className="max-w-6xl mx-auto">
          <div className="bg-dark-lighter/30 rounded-2xl border border-gray-800/50 shadow-xl backdrop-blur-sm overflow-hidden">
            {/* Account Type Tabs */}
            <div className="flex justify-center bg-dark/60 p-6">
              <div className="bg-dark/70 rounded-xl p-1.5 flex border border-gray-700/50 shadow-lg">
                <button 
                  onClick={() => setAccountType('instant')}
                  className={`px-8 py-4 rounded-lg transition-all duration-300 font-medium ${
                    accountType === 'instant' 
                      ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20' 
                      : 'text-gray-400 hover:text-white hover:bg-dark-light'
                  }`}
                >
                  Instant
                </button>
                <button 
                  onClick={() => setAccountType('oneStep')}
                  className={`px-8 py-4 rounded-lg transition-all duration-300 font-medium ${
                    accountType === 'oneStep' 
                      ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20' 
                      : 'text-gray-400 hover:text-white hover:bg-dark-light'
                  }`}
                >
                  1-Step
                </button>
                <button 
                  onClick={() => setAccountType('twoStep')}
                  className={`px-8 py-4 rounded-lg transition-all duration-300 font-medium ${
                    accountType === 'twoStep' 
                      ? 'bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-lg shadow-teal-500/20' 
                      : 'text-gray-400 hover:text-white hover:bg-dark-light'
                  }`}
                >
                  2-Step
                </button>
              </div>
            </div>

            <div className="text-center mt-6 mb-6">
              <p className="text-gray-300 max-w-2xl mx-auto text-lg">
                {accountTypes[accountType].description}
              </p>
              <p className="text-gray-400 mt-3 text-base">
                <span className="text-teal-400 font-medium">{accountTypes[accountType].phasesLabel}</span> {accountTypes[accountType].phases > 0 ? `• ${accountTypes[accountType].phases} evaluation challenge${accountTypes[accountType].phases > 1 ? 's' : ''}` : ''}
              </p>
            </div>
            
            {/* Account Size Selector - make more premium */}
            <div className="flex flex-nowrap justify-center overflow-x-auto gap-3 p-8">
              {accountSizes
                .filter(size => (accountType !== 'instant' || size.value !== 200000) && 
                               (pricingStructure[accountType][size.value as AccountSizeKey] > 0))
                .map((size) => (
                <div 
                  key={size.id}
                  onClick={() => setAccountSize(size.value)}
                  className={`relative cursor-pointer p-2 text-center rounded-xl transition-all duration-300 border flex-shrink-0 ${
                    accountSize === size.value 
                      ? 'bg-gradient-to-b from-teal-600/80 to-teal-700/80 text-white border-teal-400/30 shadow-lg shadow-teal-500/20 transform scale-105' 
                      : 'bg-dark/80 hover:bg-dark-light/80 text-gray-300 border-gray-800/50 hover:border-gray-700/50'
                  }`}
                  style={{ minWidth: '60px' }}
                >
                  {size.discountPercent > 0 && (
                    <div className="absolute -top-3 -right-2 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-[10px] font-bold py-1 px-2 rounded-full transform rotate-3 shadow-lg">
                      {size.discountPercent}% OFF
                    </div>
                  )}
                  <div className="font-bold text-lg">{size.label}</div>
                </div>
              ))}
            </div>
            
            {/* Trading Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6 pt-0">
              {/* Left Column - Trading Rules */}
              <div className="col-span-2 bg-dark/40 rounded-xl p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-y-8 gap-x-12">
                  {/* Profit Target Phase 1 */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="8 12 12 16 16 12" />
                        <line x1="12" y1="8" x2="12" y2="16" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Target</div>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-teal-300 font-semibold">PHASE 1</div>
                        <div className="text-white font-bold text-xl">8%</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Profit Target Phase 2 */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="8 12 12 16 16 12" />
                        <line x1="12" y1="8" x2="12" y2="16" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Target</div>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-teal-300 font-semibold">PHASE 2</div>
                        <div className="text-white font-bold text-xl">5%</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Max Daily Loss */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M3 3v18h18" />
                        <path d="M19 9l-5 5-4-4-3 3" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Max Daily Loss</div>
                      <div className="text-white font-bold text-xl">5%</div>
                    </div>
                  </div>
                  
                  {/* Max Total Loss */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M3 3v18h18" />
                        <path d="M19 9l-5 5-4-4-3 3" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Max Total Loss</div>
                      <div className="text-white font-bold text-xl">10%</div>
                    </div>
                  </div>
                  
                  {/* Drawdown Based */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Drawdown Based</div>
                      <div className="text-white font-bold text-xl">Balance</div>
                    </div>
                  </div>
                  
                  {/* Profit Split */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <line x1="8" y1="12" x2="16" y2="12" />
                        <line x1="12" y1="16" x2="12" y2="8" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Split</div>
                      <div className="text-white font-bold text-xl">80% / 90%</div>
                    </div>
                  </div>
                  
                  {/* Payout Frequency */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                        <line x1="16" y1="2" x2="16" y2="6" />
                        <line x1="8" y1="2" x2="8" y2="6" />
                        <line x1="3" y1="10" x2="21" y2="10" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Payout Frequency</div>
                      <div className="text-white font-bold text-xl">Weekly</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Right Column - Price Details - more premium styling */}
              <div className="bg-gradient-to-b from-dark/60 to-dark/40 backdrop-blur-sm rounded-xl p-8 flex flex-col border border-gray-800/50 shadow-xl">
                <div className="mb-auto">
                  <div className="text-center mb-8">
                    <div className="text-gray-400 text-sm uppercase tracking-wider">Virtual Capital</div>
                    <div className="text-4xl font-bold text-white my-3">${accountSize.toLocaleString()}</div>
                    <div className="inline-block px-4 py-1.5 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-sm font-bold rounded-full shadow-lg">
                      20% OFF
                    </div>
                  </div>
                  
                  <div className="mb-8 text-center">
                    <div className="text-sm text-gray-400 uppercase tracking-wider mb-2">PRICE</div>
                    <div className="flex items-center justify-center">
                      <div className="text-5xl font-bold bg-gradient-to-r from-teal-400 to-emerald-400 text-transparent bg-clip-text">${price}</div>
                      <div className="text-2xl text-gray-500 line-through ml-3">${originalPrice}</div>
                    </div>
                    <div className="mt-3 inline-flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-400 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-300 text-sm font-medium">Save ${savings}</span>
                    </div>
                  </div>
                </div>
                
                {/* CTA Button - more premium */}
                <Link 
                  href={`