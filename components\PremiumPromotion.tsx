import React from 'react';

interface PremiumPromotionProps {
  couponCode?: string;
  discountPercent?: number;
  expiryTime?: Date;
}

const PremiumPromotion: React.FC<PremiumPromotionProps> = ({
  couponCode = "INSTANT60",
  discountPercent = 60,
  expiryTime = new Date(Date.now() + 86400000) // 24 hours from now
}) => {
  return (
    <div className="relative bg-gradient-to-r from-teal-900/80 to-teal-800/90 rounded-lg overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_50%,rgba(45,212,191,0.1),transparent_70%)]"></div>
        <div className="absolute right-0 bottom-0 w-32 h-32 -translate-y-6 translate-x-6">
          <img 
            src="/shield.svg" 
            alt="" 
            className="w-full h-full opacity-10"
            onError={(e) => e.currentTarget.style.display = 'none'} 
          />
        </div>
        <div className="absolute left-0 top-0 w-32 h-32 -translate-y-6 -translate-x-6">
          <img 
            src="/bitcoin.svg" 
            alt="" 
            className="w-full h-full opacity-10"
            onError={(e) => e.currentTarget.style.display = 'none'} 
          />
        </div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 p-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="text-xl md:text-2xl font-bold text-white">Save {discountPercent}% on Premium Trading Accounts</h3>
            <div className="mt-1 flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 mt-2">
                <div className="bg-teal-700 px-3 py-1 rounded text-white font-mono font-bold">{couponCode}</div>
                <button 
                  className="text-teal-400 text-sm hover:text-teal-300 transition-colors"
                  onClick={() => {
                    navigator.clipboard.writeText(couponCode);
                    alert("Coupon code copied to clipboard!");
                  }}
                >
                  Copy
                </button>
              </div>
              
              <div className="flex items-center mt-2">
                <span className="text-sm text-teal-200">
                  ${5}K
                </span>
                <span className="mx-1 text-gray-400">|</span>
                <span className="text-sm text-teal-200">
                  ${10}K
                </span>
                <span className="mx-1 text-gray-400">|</span>
                <span className="text-sm text-teal-200">
                  ${40}K
                </span>
              </div>
            </div>
          </div>
          
          <div className="bg-teal-800/50 px-3 py-2 rounded-md">
            <div className="text-xs text-teal-200">Expires In</div>
            <div className="text-white font-medium">
              {Math.floor((expiryTime.getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PremiumPromotion; 