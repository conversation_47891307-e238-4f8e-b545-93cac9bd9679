'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import PremiumPromotion from './PremiumPromotion';

// Pricing plan data
const pricingPlans = {
  instant: [
    {
      name: '$5,000',
      price: '79',
      period: 'one-time fee',
      description: 'Perfect for beginner traders looking to prove their strategy works.',
      features: [
        '10-day trading period',
        'Max 5% daily drawdown',
        '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 80% profit share',
        'Trading dashboard access',
        '24/7 support',
      ],
      popular: false,
    },
    {
      name: '$10,000',
    price: '99',
      period: 'one-time fee',
      description: 'Perfect for beginners with some trading experience.',
    features: [
      '10-day trading period',
      'Max 5% daily drawdown',
      '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 80% profit share',
      'Trading dashboard access',
      '24/7 support',
    ],
    popular: false,
  },
  {
      name: '$25,000',
    price: '249',
      period: 'one-time fee',
      description: 'Our most popular option for intermediate traders with a proven strategy.',
    features: [
        'No time limits',
      'Max 5% daily drawdown',
      '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 85% profit share',
        'Advanced metrics dashboard',
      'Priority support',
        'Scaling opportunities',
    ],
    popular: true,
  },
  {
      name: '$50,000',
      price: '349',
      period: 'one-time fee',
      description: 'For experienced traders ready to manage substantial capital.',
      features: [
        'No time limits',
        'Max 5% daily drawdown',
        '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 85% profit share',
        'Advanced metrics dashboard',
        'Priority support',
        'Scaling opportunities',
      ],
      popular: false,
    },
    {
      name: '$100,000',
    price: '499',
      period: 'one-time fee',
      description: 'For experienced traders ready to manage larger capital amounts.',
    features: [
        'No time limits',
        'Max 5% daily drawdown',
        '10% overall drawdown limit',
        'Profit target: 8%',
      'Up to 90% profit share',
        'Advanced analytics',
        'VIP support',
        'Scaling opportunities',
        'Exclusive trader community',
      ],
      popular: false,
    },
    {
      name: '$200,000',
      price: '899',
      period: 'one-time fee',
      description: 'Our premium option for professional traders managing significant capital.',
      features: [
        'No time limits',
      'Max 5% daily drawdown',
      '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 90% profit share',
        'Advanced analytics',
      'VIP support',
        'Scaling opportunities',
        'Exclusive trader community',
      ],
      popular: false,
    },
  ],
  oneStep: [
    {
      name: '$1,000',
      price: '49',
      period: 'one-time fee',
      description: 'Lowest entry to test your trading strategy with minimal risk.',
      features: [
        '10-day trading period',
        'Max 5% daily drawdown',
        '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 75% profit share',
        'Basic dashboard access',
        '24/7 support',
      ],
      popular: false,
    },
    // Add the rest of the account sizes here (5k, 10k, 25k, 50k, 100k, 200k)
  ],
  twoStep: [
    {
      name: '$1,000',
      price: '29',
      period: 'one-time fee',
      description: 'Most affordable entry point for new traders to evaluate their strategies.',
      features: [
        '10-day trading period',
        'Max 5% daily drawdown',
        '10% overall drawdown limit',
        'Profit target: 8%',
        'Up to 70% profit share',
        'Basic dashboard access',
        '24/7 support',
      ],
    popular: false,
    },
    // Add the rest of the account sizes here (5k, 10k, 25k, 50k, 100k, 200k)
  ]
};

// Add the rest of the account sizes to oneStep and twoStep
for (let i = 0; i < pricingPlans.instant.length; i++) {
  const plan = pricingPlans.instant[i];
  pricingPlans.oneStep.push({
    ...plan,
    price: Math.round(parseInt(plan.price) * 0.8).toString(),
    popular: i === 2, // Make 25k popular
  });
  pricingPlans.twoStep.push({
    ...plan,
    price: Math.round(parseInt(plan.price) * 0.6).toString(),
    popular: i === 2, // Make 25k popular
  });
}

// Different pricing based on account type
const accountTypePricing = {
  instant: {
    multiplier: 1,
    description: 'Get instant access to a funded account without taking any challenges',
    features: {
      additional: ['Instant account access', 'No evaluation phases', 'Higher initial fee']
    },
    cta: 'Get Instant Access'
  },
  oneStep: {
    multiplier: 1,
    description: 'Complete a single evaluation phase to earn your funded account',
    features: {
      additional: ['Single evaluation phase', 'Faster funding timeline', 'Balanced pricing']
    },
    cta: 'Start 1-Step Challenge'
  },
  twoStep: {
    multiplier: 1,
    description: 'Our most affordable option with two evaluation phases',
    features: {
      additional: ['Two evaluation phases', 'Most affordable entry', 'Proven trading skills']
    },
    cta: 'Start 2-Step Challenge'
  }
};

const PricingSection = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [accountType, setAccountType] = useState<'instant' | 'oneStep' | 'twoStep'>('twoStep');
  const [selectedSize, setSelectedSize] = useState('$5,000');
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // Get the current plans based on account type
  const getCurrentPlans = () => {
    return pricingPlans[accountType];
  };

  // Get selected plan details
  const getSelectedPlan = () => {
    return getCurrentPlans().find(plan => plan.name === selectedSize) || getCurrentPlans()[0];
  };

  // Features based on account type
  const getFeatures = (planFeatures: string[]) => {
    const additionalFeatures = accountTypePricing[accountType].features.additional;
    
    // Replace first 3 features with account type specific features
    const combinedFeatures = [...additionalFeatures];
    
    // Add remaining features from the plan
    for (let i = 3; i < planFeatures.length; i++) {
      combinedFeatures.push(planFeatures[i]);
    }
    
    return combinedFeatures;
  };

  return (
    <section 
      id="pricing" 
      ref={sectionRef}
      className="py-24 relative"
    >
      {/* Decorative elements */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Grid pattern */}
        <div 
          className="absolute inset-0 opacity-5" 
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '30px 30px'
          }}
        />
        
        {/* Geometric shapes */}
        <div className="geometric-shape shape-teal-glow w-[700px] h-[700px] -bottom-[300px] -right-[300px] opacity-20"></div>
        <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] -top-[200px] -left-[200px] opacity-20"></div>

        {/* Additional decorative elements */}
        <div className="absolute top-1/4 left-1/3 w-64 h-64 rounded-full bg-teal-500/5 blur-[100px]"></div>
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 rounded-full bg-indigo-500/5 blur-[120px]"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Heading with animated underline */}
        <div className="text-center mb-16">
          <div className="inline-block">
            <h3 className="section-subheading relative">
              <span className="relative z-10">Premium Trading Solutions</span>
              <span className="absolute bottom-0 left-1/4 right-1/4 h-[3px] bg-gradient-to-r from-teal-500/0 via-teal-500 to-teal-500/0"></span>
            </h3>
          </div>
          <h2 className="section-heading mb-6 text-4xl md:text-5xl font-bold">
            Trading <span className="bg-gradient-to-r from-teal-400 to-teal-500 text-transparent bg-clip-text">Challenges</span>
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto text-lg">
            Select an account type and size that aligns with your trading strategy and goals.
            <span className="text-teal-400 ml-2 font-medium">One-time payment, no subscriptions.</span>
          </p>
        </div>

        {/* Account Type Selection */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-dark-lighter/30 p-6 rounded-2xl border border-gray-800/50 shadow-xl backdrop-blur-sm">
            {/* Header */}
            <div className="text-center mb-6">
              <h3 className="text-2xl font-semibold text-white">Choose Your Account Type</h3>
              <p className="text-gray-400 text-sm mt-2">Select the evaluation process that works best for you</p>
            </div>
            
            {/* Account Types Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div 
                className={`relative cursor-pointer rounded-xl p-6 transition-all duration-300 ${
                  accountType === 'instant' 
                    ? 'bg-gradient-to-br from-teal-700/50 to-teal-900/70 border border-teal-500/30' 
                    : 'bg-dark-lighter/80 border border-gray-800/50 hover:border-gray-700'
                }`}
                onClick={() => setAccountType('instant')}
              >
                <h4 className="text-xl font-bold text-white mb-2">Instant</h4>
                <p className="text-sm text-gray-400">No Evaluation</p>
                
                {accountType === 'instant' && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-teal-400 rounded-full"></div>
                )}
              </div>
              
              <div 
                className={`relative cursor-pointer rounded-xl p-6 transition-all duration-300 ${
                  accountType === 'oneStep' 
                    ? 'bg-gradient-to-br from-teal-700/50 to-teal-900/70 border border-teal-500/30' 
                    : 'bg-dark-lighter/80 border border-gray-800/50 hover:border-gray-700'
                }`}
                onClick={() => setAccountType('oneStep')}
              >
                <h4 className="text-xl font-bold text-white mb-2">1-Step</h4>
                <p className="text-sm text-gray-400">Single Phase</p>
                
                {accountType === 'oneStep' && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-teal-400 rounded-full"></div>
                )}
              </div>
              
              <div 
                className={`relative cursor-pointer rounded-xl p-6 transition-all duration-300 ${
                  accountType === 'twoStep' 
                    ? 'bg-gradient-to-br from-teal-700/50 to-teal-900/70 border border-teal-500/30' 
                    : 'bg-dark-lighter/80 border border-gray-800/50 hover:border-gray-700'
                }`}
                onClick={() => setAccountType('twoStep')}
              >
                <h4 className="text-xl font-bold text-white mb-2">2-Step</h4>
                <p className="text-sm text-gray-400">Two Phases</p>
                
                {accountType === 'twoStep' && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-teal-400 rounded-full"></div>
                )}
              </div>
            </div>
            
            {/* Description */}
            <div className="text-center p-4 bg-dark/40 rounded-xl">
              <p className="text-gray-300">
                  {accountTypePricing[accountType].description}
                </p>
            </div>
          </div>
        </div>

        {/* Modern Trading Account Details */}
        <div className="max-w-6xl mx-auto">
          <div className="bg-dark-lighter/30 rounded-2xl border border-gray-800/50 shadow-xl backdrop-blur-sm overflow-hidden">
            {/* Account Type Tabs */}
            <div className="flex justify-center bg-dark/60 rounded-t-xl p-2">
              <div className="bg-dark/50 p-1 rounded-full flex">
                <button 
                  className={`relative px-6 py-3 rounded-full transition-all duration-300 ${
                    accountType === 'twoStep' 
                      ? 'bg-teal-600 text-white' 
                      : 'text-gray-400 hover:text-white'
                  }`}
                  onClick={() => setAccountType('twoStep')}
            >
                  2-Step
                </button>
                <button 
                  className={`relative px-6 py-3 rounded-full transition-all duration-300 ${
                    accountType === 'oneStep' 
                      ? 'bg-teal-600 text-white' 
                      : 'text-gray-400 hover:text-white'
                  }`}
                  onClick={() => setAccountType('oneStep')}
                >
                  1-Step
                </button>
              </div>
            </div>
            
            {/* Account Size Selection */}
            <div className="grid grid-cols-3 sm:grid-cols-6 gap-2 p-6">
              {getCurrentPlans().map((plan, index) => {
                // Add discount badges for sizes
                const hasDiscount = plan.name !== '$1,000';
                
                return (
                  <div 
                    key={index}
                    className={`relative cursor-pointer p-4 text-center rounded-xl transition-all duration-300 ${
                      selectedSize === plan.name 
                        ? 'bg-teal-600 text-white' 
                        : 'bg-dark hover:bg-dark-light text-gray-300'
                    }`}
                    onClick={() => setSelectedSize(plan.name)}
                  >
                    {hasDiscount && (
                      <div className="absolute -top-2 -right-2 bg-yellow-400 text-dark text-xs font-bold py-1 px-2 rounded-full transform rotate-12">
                        20% OFF
                </div>
              )}
                    <div className="font-bold">{plan.name.replace(',', '')}</div>
                  </div>
                );
              })}
            </div>
            
            {/* Account Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6 pt-0">
              {/* Left Column - Account Details */}
              <div className="col-span-2 bg-dark/40 rounded-xl p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-y-8 gap-x-12">
                  {/* Profit Target Phase 1 */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M16 12l-4 4-4-4" />
                        <path d="M12 8v8" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Target</div>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-teal-300 font-semibold">PHASE 1</div>
                        <div className="text-white font-bold text-xl">8%</div>
                      </div>
                    </div>
                </div>
                
                  {/* Profit Target Phase 2 */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M16 12l-4 4-4-4" />
                        <path d="M12 8v8" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Target</div>
                      <div className="flex justify-between items-center">
                        <div className="text-xs text-teal-300 font-semibold">PHASE 2</div>
                        <div className="text-white font-bold text-xl">5%</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Max Daily Loss */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M3 3v18h18" />
                        <path d="M19 9l-5 5-4-4-3 3" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Max Daily Loss</div>
                      <div className="text-white font-bold text-xl">5%</div>
                    </div>
                  </div>
                  
                  {/* Max Total Loss */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M3 3v18h18" />
                        <path d="M19 9l-5 5-4-4-3 3" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Max Total Loss</div>
                      <div className="text-white font-bold text-xl">10%</div>
                    </div>
                  </div>
                  
                  {/* Drawdown Based */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Drawdown Based</div>
                      <div className="text-white font-bold text-xl">Balance</div>
                    </div>
                </div>
                
                  {/* Profit Split */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <polyline points="8 12 12 16 16 12" />
                        <line x1="12" y1="8" x2="12" y2="16" />
                        </svg>
                      </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Profit Split</div>
                      <div className="text-white font-bold text-xl">80% / 90%</div>
                    </div>
                  </div>
                  
                  {/* Payout Frequency */}
                  <div className="flex items-center">
                    <div className="text-teal-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                        <line x1="16" y1="2" x2="16" y2="6" />
                        <line x1="8" y1="2" x2="8" y2="6" />
                        <line x1="3" y1="10" x2="21" y2="10" />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xs text-teal-400 uppercase tracking-wide font-semibold">Payout Frequency</div>
                      <div className="text-white font-bold text-xl">Weekly</div>
                </div>
              </div>
            </div>
              </div>
              
              {/* Right Column - Pricing */}
              <div className="bg-dark/40 rounded-xl p-6 flex flex-col">
                {/* Plan details */}
                <div className="mb-auto">
                  <div className="text-center mb-6">
                    <div className="text-gray-400 text-sm">Virtual Capital</div>
                    <div className="text-3xl font-bold text-white mb-3">{getSelectedPlan().name}</div>
                    <div className="inline-block px-3 py-1 bg-yellow-400 text-dark text-sm font-bold rounded-full">
                      20% OFF
                </div>
                </div>
                
                  <div className="mb-6 text-center">
                    <div className="text-sm text-gray-400 mb-1">Refundable Fee</div>
                    <div className="flex items-center justify-center">
                      <div className="text-4xl font-bold text-teal-400">${getSelectedPlan().price}</div>
                      <div className="text-2xl text-gray-500 line-through ml-2">${Math.round(parseInt(getSelectedPlan().price) * 1.25)}</div>
                    </div>
                    <div className="mt-2 inline-flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-teal-400 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-400 text-xs">Save ${Math.round(parseInt(getSelectedPlan().price) * 0.25)}</span>
                    </div>
                  </div>
                </div>
                
                {/* CTA button */}
                <button className="w-full py-4 px-6 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold rounded-xl shadow-lg shadow-teal-500/20 hover:shadow-teal-500/40 transition-all duration-300 transform hover:translate-y-[-2px]">
                  Start Now
                </button>
                
                {/* Payment methods */}
                <div className="mt-4 text-center">
                  <div className="flex justify-center items-center space-x-3 text-gray-400">
                    <span>VISA</span>
                    <span>•</span>
                    <span>stripe</span>
                    <span>•</span>
                    <span>Apple Pay</span>
                    <span>•</span>
                    <span>BTC</span>
                    <span>•</span>
                    <span>ETH</span>
                  </div>
                      </div>
                
                {/* Promo */}
                <div className="mt-6 bg-gradient-to-r from-teal-900/80 to-teal-800/60 p-4 rounded-lg">
                  <div className="flex items-center">
                    <span className="text-teal-300 mr-2">🎉</span>
                    <div>
                      <div className="text-white font-semibold">We're Celebrating</div>
                      <div className="text-teal-300 font-bold text-xl">IMPROVED</div>
                      <div className="text-white text-xs">FUNDING CONDITIONS</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Trust badges and additional info */}
        <div className="mt-20 max-w-4xl mx-auto">
          <div className="relative bg-dark-lighter/30 backdrop-blur-sm rounded-2xl p-8 border border-gray-800/50 shadow-lg overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden opacity-10 pointer-events-none">
              <div className="absolute -top-40 -right-40 w-96 h-96 bg-teal-500/20 rounded-full blur-[100px]"></div>
              <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-indigo-500/20 rounded-full blur-[100px]"></div>
              <svg className="absolute bottom-0 right-0 w-64 h-64 text-teal-500/5" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <path fill="currentColor" d="M55,-77.2C71.9,-68.9,86.5,-56.2,93.7,-40C100.9,-23.7,100.8,-3.9,96.2,14.7C91.6,33.3,82.5,50.8,68.1,62.6C53.7,74.3,33.9,80.4,13.8,83.3C-6.3,86.2,-26.7,85.9,-40.9,76.5C-55.1,67.1,-63.1,48.6,-73.4,29.8C-83.7,11,-96.3,-8.1,-93.1,-24.8C-89.9,-41.4,-71,-55.6,-52.8,-64.3C-34.5,-73,-17.3,-76.1,0.5,-76.8C18.2,-77.5,36.4,-76,55,-77.2Z" transform="translate(100 100)" />
              </svg>
            </div>
            
            <div className="text-center mb-8 relative">
              <span className="inline-block px-4 py-1 bg-teal-500/10 text-teal-400 text-xs font-semibold rounded-full uppercase tracking-wide mb-3">
                Transparent Pricing
              </span>
              <h3 className="text-2xl font-semibold text-white mb-4">No Hidden Fees or Subscriptions</h3>
              <p className="text-gray-400 max-w-3xl mx-auto">
                All our challenges require a one-time payment. No monthly subscription fees, no platform fees, no hidden costs.
                You'll receive a full refund of your challenge fee after your first profitable withdrawal.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 relative">
              <div className="bg-dark-lighter/70 rounded-xl p-5 border border-gray-800/50 hover:border-teal-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-teal-500/5 group">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mr-3 group-hover:bg-teal-500/20 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="text-white font-medium group-hover:text-teal-300 transition-colors duration-300">Free Demo Account</h4>
                </div>
                <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors duration-300">Practice with our platform using a free demo account before you commit to a challenge.</p>
              </div>
              
              <div className="bg-dark-lighter/70 rounded-xl p-5 border border-gray-800/50 hover:border-teal-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-teal-500/5 group">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mr-3 group-hover:bg-teal-500/20 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                  </div>
                  <h4 className="text-white font-medium group-hover:text-teal-300 transition-colors duration-300">Challenge Fee Refund</h4>
                </div>
                <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors duration-300">Your initial challenge fee is fully refundable after your first profitable withdrawal.</p>
              </div>
              
              <div className="bg-dark-lighter/70 rounded-xl p-5 border border-gray-800/50 hover:border-teal-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-teal-500/5 group">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full bg-teal-500/10 flex items-center justify-center mr-3 group-hover:bg-teal-500/20 transition-all duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h4 className="text-white font-medium group-hover:text-teal-300 transition-colors duration-300">Weekly Payouts</h4>
                </div>
                <p className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors duration-300">Regular weekly profit payouts via your preferred payment method.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection; 