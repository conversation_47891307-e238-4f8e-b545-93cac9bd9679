'use client';

import { useEffect, useRef, useState } from 'react';

declare global {
  interface Window {
    TradingView: any;
  }
}

const timeframes = [
  { label: '1D', value: '1' },
  { label: '1W', value: 'W' },
  { label: '1M', value: 'M' },
];

const popularSymbols = [
  { label: 'AAPL', value: 'NASDAQ:AAPL' },
  { label: 'TSLA', value: 'NASDAQ:TSLA' },
  { label: 'BTC', value: 'BINANCE:BTCUSDT' },
];

const TradingChart = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState('1');
  const [selectedSymbol, setSelectedSymbol] = useState('NASDAQ:AAPL');

  const createWidget = () => {
    if (containerRef.current && window.TradingView) {
      // Clear previous widget if exists
      containerRef.current.innerHTML = '';
      
      new window.TradingView.widget({
        "width": "100%",
        "height": "100%",
        "symbol": selectedSymbol,
        "interval": selectedTimeframe,
        "timezone": "Etc/UTC",
        "theme": "dark",
        "style": "1",
        "locale": "en",
        "toolbar_bg": "#1a1a1a",
        "enable_publishing": false,
        "hide_side_toolbar": true,
        "hide_top_toolbar": false,
        "allow_symbol_change": true,
        "container_id": containerRef.current.id,
        "studies": [
          "RSI@tv-basicstudies",
          "MASimple@tv-basicstudies"
        ],
        "show_popup_button": false,
        "save_image": false,
        "hideideas": true,
        "withdateranges": true,
        "hide_volume": false,
        "support_host": "https://www.tradingview.com"
      });
    }
  };

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/tv.js';
    script.async = true;
    script.onload = createWidget;
    document.head.appendChild(script);

    return () => {
      script.remove();
    };
  }, []); // Initial load

  useEffect(() => {
    // Recreate widget when symbol or timeframe changes
    if (window.TradingView) {
      createWidget();
    }
  }, [selectedTimeframe, selectedSymbol]);

  return (
    <div className="w-full bg-gray-900/80 backdrop-blur-xl rounded-2xl p-2 sm:p-4 border border-gray-800/50 shadow-xl">
      <div className="mb-2 sm:mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div>
          <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-teal-300 to-teal-400 bg-clip-text text-transparent">
            Live Market Analysis
          </h3>
          <p className="text-sm sm:text-base text-gray-400 mt-1">
            Professional-grade charts with real-time market data
          </p>
        </div>
        
        {/* Controls */}
        <div className="flex flex-wrap gap-2 items-center">
          {/* Popular Symbols */}
          <div className="flex gap-1">
            {popularSymbols.map((symbol) => (
              <button
                key={symbol.value}
                onClick={() => setSelectedSymbol(symbol.value)}
                className={`px-3 py-1.5 text-sm rounded-lg transition-all duration-200 ${
                  selectedSymbol === symbol.value
                    ? 'bg-teal-500/20 text-teal-300 border border-teal-500/30'
                    : 'bg-gray-800/50 text-gray-400 border border-gray-700/30 hover:bg-gray-800'
                }`}
              >
                {symbol.label}
              </button>
            ))}
          </div>

          {/* Timeframe Selector */}
          <div className="flex gap-1">
            {timeframes.map((tf) => (
              <button
                key={tf.value}
                onClick={() => setSelectedTimeframe(tf.value)}
                className={`px-3 py-1.5 text-sm rounded-lg transition-all duration-200 ${
                  selectedTimeframe === tf.value
                    ? 'bg-teal-500/20 text-teal-300 border border-teal-500/30'
                    : 'bg-gray-800/50 text-gray-400 border border-gray-700/30 hover:bg-gray-800'
                }`}
              >
                {tf.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative w-full bg-gray-900/50 rounded-xl border border-gray-800/30">
        <div 
          id="tradingview_widget" 
          ref={containerRef}
          className="rounded-xl overflow-hidden h-[350px] sm:h-[400px] md:h-[500px] lg:h-[600px]"
        />
      </div>

      {/* Mobile Hint */}
      <div className="mt-2 text-center sm:hidden">
        <p className="text-xs text-gray-500">
          Tip: Use two fingers to zoom and scroll the chart
        </p>
      </div>
    </div>
  );
};

export default TradingChart;