'use client';

import React from 'react';

const partnerLogos = [
  {
    id: 1,
    name: 'MetaTrader',
    logo: (
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded px-4 py-1 flex items-center justify-center">
        <span className="text-gray-700 dark:text-gray-200 font-semibold">MetaTrader</span>
      </div>
    )
  },
  {
    id: 2,
    name: 'TradingView',
    logo: (
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded px-4 py-1 flex items-center justify-center">
        <span className="text-gray-700 dark:text-gray-200 font-semibold">TradingView</span>
      </div>
    )
  },
  {
    id: 3,
    name: 'cTrader',
    logo: (
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded px-4 py-1 flex items-center justify-center">
        <span className="text-gray-700 dark:text-gray-200 font-semibold">cTrader</span>
      </div>
    )
  },
  {
    id: 4,
    name: '<PERSON>e',
    logo: (
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded px-4 py-1 flex items-center justify-center">
        <span className="text-gray-700 dark:text-gray-200 font-semibold">Stripe</span>
      </div>
    )
  },
  {
    id: 5,
    name: 'PayPal',
    logo: (
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded px-4 py-1 flex items-center justify-center">
        <span className="text-gray-700 dark:text-gray-200 font-semibold">PayPal</span>
      </div>
    )
  }
];

const TrustSection = () => {
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-base font-semibold text-indigo-600 dark:text-indigo-400 tracking-wide uppercase">Trusted Worldwide</h2>
          <p className="mt-2 text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
            Join Thousands of Successful Traders
          </p>
          <p className="mt-4 max-w-2xl text-xl text-gray-500 dark:text-gray-300 mx-auto">
            Our funded traders are making consistent profits across the globe with our capital.
          </p>
        </div>

        {/* Trust metrics */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3 mb-16">
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8 text-center">
            <div className="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">25,000+</div>
            <p className="text-gray-600 dark:text-gray-300 text-lg">Active Traders</p>
          </div>
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8 text-center">
            <div className="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">$50M+</div>
            <p className="text-gray-600 dark:text-gray-300 text-lg">Paid to Traders</p>
          </div>
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8 text-center">
            <div className="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">150+</div>
            <p className="text-gray-600 dark:text-gray-300 text-lg">Countries Served</p>
          </div>
        </div>

        {/* Testimonial placeholders */}
        <div className="text-center mb-10">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
            What Our Traders Say
          </h3>
          <p className="mt-2 text-gray-500 dark:text-gray-400">
            Hear from our community of funded traders
          </p>
        </div>

        {/* Logos of partners */}
        <div className="mt-16">
          <p className="text-center text-gray-600 dark:text-gray-400 text-sm uppercase tracking-wider font-medium mb-8">
            Platforms and Payment Partners
          </p>
          <div className="grid grid-cols-2 gap-6 md:grid-cols-5">
            {partnerLogos.map((partner) => (
              <div key={partner.id} className="flex justify-center">
                {partner.logo}
              </div>
            ))}
          </div>
        </div>

        {/* TODO: Connect to actual trader testimonials from backend */}
        <div className="text-center mt-12">
          <p className="text-gray-500 dark:text-gray-400">
            Want to see more success stories? <a href="#" className="text-indigo-600 font-medium">Visit our trader showcase</a>
          </p>
        </div>
      </div>
    </section>
  );
};

export default TrustSection; 