'use client';

import { useState } from 'react';
import { ContainerAccount, adminService } from '@/services/adminService';

interface AddContainerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContainerAdded: (container: ContainerAccount) => void;
}

export default function AddContainerModal({ isOpen, onClose, onContainerAdded }: AddContainerModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState<{
    platform: string;
    server: string;
    platform_login: string;
    platform_password: string;
    account_size: string;
    account_type: string;
  }>({
    platform: 'mt5',
    server: '',
    platform_login: '',
    platform_password: '',
    account_size: '',
    account_type: 'phase1',
  });

  if (!isOpen) return null;

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Call the API to create a new container
      const newContainer = await adminService.createContainer(formData);

      // If successful, close the modal and refresh the container list
      if (newContainer) {
        onContainerAdded(newContainer);
        onClose();

        // Reset form data
        setFormData({
          platform: 'mt5',
          server: '',
          platform_login: '',
          platform_password: '',
          account_size: '',
          account_type: 'phase1',
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create container. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-black opacity-75"></div>
        </div>

        {/* Modal content */}
        <div className="inline-block align-bottom bg-[#0F1A2E] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-[#070F1B] px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Add New Container Account</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Body */}
          <form onSubmit={handleSubmit}>
            <div className="px-6 py-4">
              {error && (
                <div className="mb-4 bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg">
                  {error}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="mb-4">
                  <label htmlFor="platform" className="block text-gray-400 mb-2">
                    Platform
                  </label>
                  <select
                    id="platform"
                    name="platform"
                    value={formData.platform}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                  >
                    <option value="mt5">MT5</option>
                    <option value="mt4">MT4</option>
                  </select>
                </div>

                <div className="mb-4">
                  <label htmlFor="account_type" className="block text-gray-400 mb-2">
                    Account Type
                  </label>
                  <select
                    id="account_type"
                    name="account_type"
                    value={formData.account_type}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                  >
                    <option value="">Select Account Type</option>
                    <option value="instant">INSTANT</option>
                    <option value="phase1">Phase 1</option>
                    <option value="phase2">Phase 2</option>
                    <option value="live">Live</option>
                  </select>
                </div>

                <div className="mb-4">
                  <label htmlFor="server" className="block text-gray-400 mb-2">
                    Server
                  </label>
                  <input
                    type="text"
                    id="server"
                    name="server"
                    value={formData.server}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                    placeholder="Enter server name"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="account_size" className="block text-gray-400 mb-2">
                    Account Size
                  </label>
                  <div className="relative">
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">$</span>
                    <input
                      type="text"
                      id="account_size"
                      name="account_size"
                      value={formData.account_size}
                      onChange={handleChange}
                      className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 pl-8 pr-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                      placeholder="Enter account size"
                      required
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="platform_login" className="block text-gray-400 mb-2">
                    Platform Login
                  </label>
                  <input
                    type="text"
                    id="platform_login"
                    name="platform_login"
                    value={formData.platform_login}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                    placeholder="Enter platform login"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="platform_password" className="block text-gray-400 mb-2">
                    Platform Password
                  </label>
                  <input
                    type="password"
                    id="platform_password"
                    name="platform_password"
                    value={formData.platform_password}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                    placeholder="Enter platform password"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-[#070F1B] px-6 py-4 border-t border-teal-500/20 flex justify-end">
              <button
                type="button"
                onClick={onClose}
                className="mr-3 px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-teal-600 text-white text-sm font-medium rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                  </div>
                ) : (
                  'Create Container'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}