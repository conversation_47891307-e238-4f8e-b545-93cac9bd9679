'use client';

import { AdminDashboardSummary } from '@/services/adminService';
import Link from 'next/link';
import StatCard from './StatCard';

interface DashboardSummaryProps {
  summary: AdminDashboardSummary;
}

export default function DashboardSummary({ summary }: DashboardSummaryProps) {
  return (
    <>
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Orders"
          value={summary?.totalOrders || 0}
          icon={
            <svg className="w-8 h-8 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          }
        />
        <StatCard
          title="Total Users"
          value={summary?.totalUsers || 0}
          icon={
            <svg className="w-8 h-8 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          }
        />
        <StatCard
          title="Completed Orders"
          value={summary?.orderSummary.completed || 0}
          icon={
            <svg className="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
        <StatCard
          title="Failed Orders"
          value={summary?.orderSummary.failed || 0}
          icon={
            <svg className="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
      </div>

      {/* Order Status Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Link href="/admin/orders/stage-2" className="block">
          <StatCard
            title="Stage 2 Orders"
            value={summary?.orderSummary.stage_2 || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href="/admin/orders/live" className="block">
          <StatCard
            title="Live Orders"
            value={summary?.orderSummary.live || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href="/admin/orders/running" className="block">
          <StatCard
            title="Running Orders"
            value={summary?.orderSummary.running || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href="/admin/orders/passed" className="block">
          <StatCard
            title="Passed Orders"
            value={summary?.orderSummary.passed || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href="/admin/orders/all" className="block">
          <StatCard
            title="Pending Orders"
            value={summary?.orderSummary.incomplete || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href="/admin/orders/failed" className="block">
          <StatCard
            title="Failed Orders"
            value={summary?.orderSummary.failed || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
        <Link href="/admin/certificates" className="block">
          <StatCard
            title="Certificates"
            value={summary?.orderSummary.certificates || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
            icon={
              <svg className="w-8 h-8 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            }
          />
        </Link>
        <Link href="/admin/users" className="block">
          <StatCard
            title="Verified Users"
            value={summary?.totalUsers || 0}
            className="hover:border-teal-500/40 transition-all duration-300 cursor-pointer"
          />
        </Link>
      </div>
    </>
  );
}
