'use client';

import { useState } from 'react';
import { ContainerAccount, horizonAdminService } from '@/services/horizonAdminService';

interface HorizonAddContainerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContainerAdded: (container: ContainerAccount) => void;
}

export default function HorizonAddContainerModal({ isOpen, onClose, onContainerAdded }: HorizonAddContainerModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState<{
    platform: string;
    server: string;
    platform_login: string;
    platform_password: string;
    account_size: string;
    account_type: string;
  }>({
    platform: 'mt5',
    server: '',
    platform_login: '',
    platform_password: '',
    account_size: '',
    account_type: 'phase1',
  });

  if (!isOpen) return null;

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Call the Horizon API to create a new container
      const newContainer = await horizonAdminService.createContainer(formData);

      // If successful, close the modal and refresh the container list
      if (newContainer) {
        onContainerAdded(newContainer);
        onClose();

        // Reset form data
        setFormData({
          platform: 'mt5',
          server: '',
          platform_login: '',
          platform_password: '',
          account_size: '',
          account_type: 'phase1',
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create container. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[#0F1A2E] rounded-lg p-6 w-full max-w-md mx-4 border border-orange-500/20">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Add New Container - Horizon</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-md">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Platform
            </label>
            <select
              name="platform"
              value={formData.platform}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              required
            >
              <option value="mt4">MT4</option>
              <option value="mt5">MT5</option>
              <option value="ctrader">cTrader</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Server
            </label>
            <input
              type="text"
              name="server"
              value={formData.server}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder="e.g., Horizon-Live01"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Platform Login
            </label>
            <input
              type="text"
              name="platform_login"
              value={formData.platform_login}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder="Login ID"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Platform Password
            </label>
            <input
              type="password"
              name="platform_password"
              value={formData.platform_password}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder="Password"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Account Size
            </label>
            <select
              name="account_size"
              value={formData.account_size}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              required
            >
              <option value="">Select Account Size</option>
              <option value="1000">$1,000</option>
              <option value="3000">$3,000</option>
              <option value="5000">$5,000</option>
              <option value="10000">$10,000</option>
              <option value="25000">$25,000</option>
              <option value="50000">$50,000</option>
              <option value="100000">$100,000</option>
              <option value="200000">$200,000</option>
              <option value="500000">$500,000</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Account Type
            </label>
            <select
              name="account_type"
              value={formData.account_type}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              required
            >
              <option value="phase1">Phase 1</option>
              <option value="phase2">Phase 2</option>
              <option value="live">Live</option>
              <option value="hft_neo">HFT Neo</option>
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-md hover:bg-gray-700 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Container'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
