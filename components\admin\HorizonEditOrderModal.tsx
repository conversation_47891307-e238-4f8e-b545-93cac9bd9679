'use client';

import { useState, useEffect } from 'react';
import { Order, ContainerAccount, horizonAdminService, FailureReason } from '@/services/horizonAdminService';

type OrderStatus = 'incomplete' | 'completed' | 'failed' | 'passed' | 'stage_2' | 'live' | 'running';

interface HorizonEditOrderModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated: (order: Order) => void;
}

export default function HorizonEditOrderModal({ order, isOpen, onClose, onOrderUpdated }: HorizonEditOrderModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEdited, setIsEdited] = useState(false);
  const [isConfirmMode, setIsConfirmMode] = useState(false);
  const [containers, setContainers] = useState<ContainerAccount[]>([]);
  const [filteredContainers, setFilteredContainers] = useState<ContainerAccount[]>([]);
  const [selectedContainer, setSelectedContainer] = useState<ContainerAccount | null>(null);
  const [formData, setFormData] = useState<{
    status: OrderStatus | string;
    platform_login: string;
    platform_password: string;
    server: string;
    profit_target: string;
    session_id: string;
    terminal_id: string;
    drawdown: string;
    container_id: string;
    // Additional fields for passed orders
    account_type: 'stage2' | 'live' | '';
    // Additional fields for failed orders
    fail_reason: FailureReason | string;
    fail_date: string;
    // Additional fields for passed orders
    profit_amount: string;
    notes: string;
  }>({
    status: 'incomplete',
    platform_login: '',
    platform_password: '',
    server: '',
    profit_target: '',
    session_id: '',
    terminal_id: '',
    drawdown: '',
    container_id: '',
    account_type: '',
    fail_reason: '',
    fail_date: new Date().toISOString().split('T')[0],
    profit_amount: '',
    notes: '',
  });

  // Fetch containers when modal opens
  useEffect(() => {
    if (isOpen) {
      const fetchContainers = async () => {
        try {
          const data = await horizonAdminService.getPendingContainers();
          setContainers(data);
        } catch (error) {
          // Error handling without logging
        }
      };
      fetchContainers();
    }
  }, [isOpen]);

  // Filter containers by account size when order or containers change
  useEffect(() => {
    if (order && containers.length > 0) {
      const filtered = containers.filter(container => 
        container.account_size === order.account_size
      );
      setFilteredContainers(filtered);
    } else {
      setFilteredContainers(containers);
    }
  }, [order, containers]);

  // Reset form when order changes
  useEffect(() => {
    if (order) {
      setFormData({
        status: order.status || 'incomplete',
        platform_login: order.platform_login || '',
        platform_password: order.platform_password || '',
        server: order.server || '',
        profit_target: order.profit_target?.toString() || '',
        session_id: order.session_id || '',
        terminal_id: order.terminal_id?.toString() || '',
        drawdown: order.drawdown?.toString() || '',
        container_id: order.container_id?.toString() || '',
        account_type: '',
        // Additional fields for failed orders
        fail_reason: '',
        fail_date: new Date().toISOString().split('T')[0],
        profit_amount: order.profit_amount?.toString() || '',
        notes: order.notes || '',
      });
      setIsEdited(false);
      setIsConfirmMode(false);
    }
  }, [order]);

  // Update form when container is selected
  useEffect(() => {
    if (selectedContainer) {
      setFormData(prev => ({
        ...prev,
        platform_login: selectedContainer.platform_login,
        platform_password: selectedContainer.platform_password,
        server: selectedContainer.server,
        container_id: selectedContainer.id.toString(),
      }));
      setIsEdited(true);
    }
  }, [selectedContainer]);

  if (!isOpen || !order) return null;

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setIsEdited(true);
    setIsConfirmMode(false);
  };

  // Handle container selection
  const handleContainerSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const containerId = e.target.value;
    const container = filteredContainers.find(c => c.id.toString() === containerId);
    setSelectedContainer(container || null);
  };

  // Helper function to check if status is incomplete
  const isIncomplete = (status: OrderStatus | string): boolean => {
    return status === 'incomplete' || status === 'Pending';
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isConfirmMode) {
      setIsConfirmMode(true);
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const orderIdStr = order.id.toString();
      let finalOrder: Order;

      // First check if this is a passed order being moved to stage2 or live
      if (order.status === 'passed' && formData.account_type) {
        // Edit a passed order to move to stage2 or live
        finalOrder = await horizonAdminService.editPassedOrder(orderIdStr, {
          platform_login: formData.platform_login,
          platform_password: formData.platform_password,
          server: formData.server,
          terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
          session_id: formData.session_id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0,
          account_type: formData.account_type
        });
      }
      // If completing order with a container selected
      else if (formData.status === 'completed' && formData.container_id) {
        // Assign container to order
        const assignedContainer = await horizonAdminService.assignContainerToOrder(
          parseInt(formData.container_id),
          orderIdStr,
          formData.profit_target ? parseFloat(formData.profit_target) : 0
        );

        // Update the order with the container details
        finalOrder = {
          ...order,
          status: 'completed',
          platform_login: assignedContainer.platform_login,
          platform_password: assignedContainer.platform_password,
          server: assignedContainer.server,
          container_id: assignedContainer.id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : undefined
        };
      }
      // If editing a completed order
      else if (formData.status === 'completed' && order.status === 'completed') {
        const orderDetails = {
          platform_login: formData.platform_login,
          platform_password: formData.platform_password,
          server: formData.server,
          terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
          session_id: formData.session_id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0
        };
        finalOrder = await horizonAdminService.editCompletedOrder(orderIdStr, orderDetails);
      } else if (formData.status === 'completed' && order.status !== 'completed') {
        // Complete an order without container
        const orderDetails = {
          platform_login: formData.platform_login,
          platform_password: formData.platform_password,
          server: formData.server,
          terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
          session_id: formData.session_id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0
        };
        finalOrder = await horizonAdminService.completeOrder(orderIdStr, orderDetails);
      } else if (formData.status === 'failed') {
        // Fail an order with reason and date
        const failDetails = {
          ...(formData.fail_reason && { reason: formData.fail_reason as FailureReason }),
          date: formData.fail_date
        };
        finalOrder = await horizonAdminService.failOrder(orderIdStr, failDetails);
      } else if (formData.status === 'passed') {
        // Pass an order with profit amount and notes
        const passedOrderDetails = {
          profit_amount: formData.profit_amount ? parseFloat(formData.profit_amount) : 0,
          notes: formData.notes
        };
        finalOrder = await horizonAdminService.passOrder(orderIdStr, passedOrderDetails);
      } else {
        throw new Error('Invalid order status or operation');
      }

      onOrderUpdated(finalOrder);
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to update order. Please try again.');
      setIsConfirmMode(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[#0F1A2E] rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto border border-orange-500/20">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">
            Edit Order - {order.order_id} (Horizon)
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-md">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Order Status */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Order Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
            >
              <option value="incomplete">Incomplete</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="passed">Passed</option>
            </select>
          </div>

          {/* Container Selection - Only show for completing orders */}
          {formData.status === 'completed' && order.status !== 'completed' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Select Container Account (Account Size: ${order.account_size})
              </label>
              <select
                name="container_id"
                value={formData.container_id}
                onChange={handleContainerSelect}
                className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              >
                <option value="">Select a container</option>
                {filteredContainers.map((container) => (
                  <option key={container.id} value={container.id}>
                    {container.platform.toUpperCase()} - {container.account_type} (${container.account_size})
                  </option>
                ))}
              </select>
              {filteredContainers.length === 0 && (
                <p className="text-orange-400 text-sm mt-1">
                  No containers available for ${order.account_size} account size
                </p>
              )}
            </div>
          )}

          {/* Platform Login */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Platform Login
            </label>
            <input
              type="text"
              name="platform_login"
              value={formData.platform_login}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder={isIncomplete(order.status) ? "Not provided" : "Enter platform login"}
            />
          </div>

          {/* Platform Password */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Platform Password
            </label>
            <input
              type="password"
              name="platform_password"
              value={formData.platform_password}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder={isIncomplete(order.status) ? "Not provided" : "Enter platform password"}
            />
          </div>

          {/* Server */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Server
            </label>
            <input
              type="text"
              name="server"
              value={formData.server}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder={isIncomplete(order.status) ? "Not provided" : "Enter server"}
            />
          </div>

          {/* Profit Target */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Profit Target
            </label>
            <input
              type="number"
              name="profit_target"
              value={formData.profit_target}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              placeholder="Enter profit target"
            />
          </div>

          {/* Failure Reason - Only show for failed orders */}
          {formData.status === 'failed' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Failure Reason
              </label>
              <select
                name="fail_reason"
                value={formData.fail_reason}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              >
                <option value="">Select reason</option>
                <option value="2.5% daily drawdown limit hit">2.5% daily drawdown limit hit</option>
                <option value="4% daily drawdown limit hit">4% daily drawdown limit hit</option>
                <option value="Overall drawdown limit hit">Overall drawdown limit hit</option>
                <option value="Minimum Trading Days">Minimum Trading Days</option>
                <option value="OTHER">Other</option>
              </select>
            </div>
          )}

          {/* Pass Details - Only show for passed orders */}
          {formData.status === 'passed' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Profit Amount
                </label>
                <input
                  type="number"
                  name="profit_amount"
                  value={formData.profit_amount}
                  onChange={handleChange}
                  className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
                  placeholder="Enter profit amount"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Notes
                </label>
                <input
                  type="text"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
                  placeholder="Enter notes"
                />
              </div>
            </>
          )}

          {/* Account Type - Only show for passed orders */}
          {order.status === 'passed' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Move to Account Type
              </label>
              <select
                name="account_type"
                value={formData.account_type}
                onChange={handleChange}
                className="w-full px-3 py-2 bg-[#1A2332] border border-orange-500/20 rounded-md text-white focus:outline-none focus:border-orange-500"
              >
                <option value="">Select account type</option>
                <option value="stage2">Stage 2</option>
                <option value="live">Live</option>
              </select>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-md hover:bg-gray-700 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`flex-1 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                isConfirmMode 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-orange-600 hover:bg-orange-700 text-white'
              }`}
              disabled={isLoading || !isEdited}
            >
              {isLoading ? 'Processing...' : isConfirmMode ? 'Confirm Changes' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
