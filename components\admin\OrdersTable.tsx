'use client';

import { useState, useEffect } from 'react';
import { Order } from '@/services/adminService';

interface OrdersTableProps {
  orders: Order[];
  title: string;
  onOrderUpdated: (updatedOrder: Order) => void;
  onViewOrder: (order: Order) => void;
  onEditOrder: (order: Order) => void;
}

export default function OrdersTable({
  orders: initialOrders,
  title,
  onOrderUpdated,
  onViewOrder,
  onEditOrder
}: OrdersTableProps) {
  const [orders, setOrders] = useState<Order[]>(initialOrders);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingOrderId, setLoadingOrderId] = useState<string | null>(null);
  const ordersPerPage = 10;

  // Update orders when initialOrders change
  useEffect(() => {
    const sortedOrders = [...initialOrders].sort((a, b) => {
      const dateA = new Date(getRecentDate(a)).getTime();
      const dateB = new Date(getRecentDate(b)).getTime();
      return dateB - dateA;
    });
    setOrders(sortedOrders);
  }, [initialOrders]);

  // Filter orders based on search term
  const filteredOrders = orders.filter(order => {
    const searchLower = searchTerm.toLowerCase();
    return (
      (order.id && order.id.toString().toLowerCase().includes(searchLower)) ||
      (order.order_id && order.order_id.toString().toLowerCase().includes(searchLower)) ||
      (order.username && order.username.toString().toLowerCase().includes(searchLower)) ||
      (order.email && order.email.toString().toLowerCase().includes(searchLower)) ||
      (order.status && order.status.toString().toLowerCase().includes(searchLower)) ||
      (order.challenge_type && order.challenge_type.toString().toLowerCase().includes(searchLower)) ||
      (order.account_size && order.account_size.toString().toLowerCase().includes(searchLower)) ||
      (order.platform && order.platform.toString().toLowerCase().includes(searchLower)) ||
      (order.platform_login && order.platform_login.toString().toLowerCase().includes(searchLower)) ||
      (order.server && order.server.toString().toLowerCase().includes(searchLower)) ||
      (order.txid && order.txid.toString().toLowerCase().includes(searchLower))
    );
  });

  // Pagination
  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Get the most recent date from timeline or created_at
  const getRecentDate = (order: Order) => {
    // For passed orders, use pass_date if available
    if (order.pass_date) {
      return order.pass_date;
    }

    // For orders with timeline, use the most recent event date
    if (order.timeline && order.timeline.length > 0) {
      return order.timeline[0].event_date;
    }

    // Otherwise use created_at or current date
    return order.created_at || new Date().toISOString();
  };

  // Format status for display
  const formatStatus = (status: string) => {
    if (!status) return 'Pending';

    // Replace underscores with spaces and capitalize each word
    return status
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Status badge color
  const getStatusColor = (status: string) => {
    const statusLower = status?.toLowerCase() || '';

    if (statusLower.includes('complet')) {
      return 'bg-green-100 text-green-800';
    } else if (statusLower.includes('fail')) {
      return 'bg-red-100 text-red-800';
    } else if (statusLower.includes('pass')) {
      return 'bg-blue-100 text-blue-800';
    } else if (statusLower.includes('stage')) {
      return 'bg-purple-100 text-purple-800';
    } else if (statusLower.includes('live')) {
      return 'bg-yellow-100 text-yellow-800';
    } else if (statusLower.includes('run')) {
      return 'bg-indigo-100 text-indigo-800';
    } else if (statusLower.includes('incomplet') || statusLower.includes('pending')) {
      return 'bg-gray-100 text-gray-800';
    } else {
      return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
      <div className="px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
        <div className="flex items-center">
          <h2 className="text-lg font-medium text-white">{title}</h2>
          <span className="ml-2 px-2 py-1 bg-teal-500/20 rounded-full text-xs text-teal-400">
            {orders.length} total
          </span>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search by ID, TXID, user, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-teal-500"
          />
          <svg
            className="absolute right-3 top-2.5 h-4 w-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-teal-500/20">
          <thead className="bg-[#070F1B]">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Order ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Account Size
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Platform
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Date
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-teal-500/20">
            {currentOrders.length > 0 ? (
              currentOrders.map((order) => (
                <tr key={order.id} className="hover:bg-[#070F1B]/50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                    {order.order_id || order.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    <div>{order.username}</div>
                    <div className="text-xs text-gray-400">{order.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {order.account_size}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {order.platform}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                      {formatStatus(order.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {formatDate(getRecentDate(order))}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => onViewOrder(order)}
                      className="text-teal-400 hover:text-teal-300 mr-3"
                    >
                      View
                    </button>
                    <button
                      onClick={() => onEditOrder(order)}
                      className="text-teal-400 hover:text-teal-300"
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-400">
                  No orders found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {filteredOrders.length > 0 && (
        <div className="px-6 py-4 border-t border-teal-500/20 flex justify-between items-center">
          <div className="text-sm text-gray-400">
            Showing {indexOfFirstOrder + 1} to {Math.min(indexOfLastOrder, filteredOrders.length)} of {filteredOrders.length} orders
            {filteredOrders.length !== orders.length && (
              <span className="ml-2 text-teal-400">
                (filtered from {orders.length} total)
              </span>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded-md text-sm ${
                currentPage === 1
                  ? 'bg-[#070F1B]/50 text-gray-500 cursor-not-allowed'
                  : 'bg-[#070F1B] text-white hover:bg-teal-500/20'
              }`}
            >
              Previous
            </button>
            {totalPages > 2 && (
              <div className="flex items-center px-3 text-sm text-gray-400">
                Page {currentPage} of {totalPages}
              </div>
            )}
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages || totalPages === 0}
              className={`px-3 py-1 rounded-md text-sm ${
                currentPage === totalPages || totalPages === 0
                  ? 'bg-[#070F1B]/50 text-gray-500 cursor-not-allowed'
                  : 'bg-[#070F1B] text-white hover:bg-teal-500/20'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
