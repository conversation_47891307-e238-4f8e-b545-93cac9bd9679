'use client';

import { useState, useEffect } from 'react';
import { Order, adminService } from '@/services/adminService';
import ViewOrderModal from './ViewOrderModal';

interface RunningOrdersTableProps {
  orders: Order[];
  title: string;
  onOrderUpdated: (updatedOrder: Order) => void;
}

export default function RunningOrdersTable({ orders: initialOrders, title, onOrderUpdated }: RunningOrdersTableProps) {
  const [orders, setOrders] = useState<Order[]>(initialOrders);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingOrderId, setLoadingOrderId] = useState<string | number | null>(null);
  const ordersPerPage = 10;

  // Update orders when initialOrders change
  useEffect(() => {
    setOrders(initialOrders);
  }, [initialOrders]);

  // Filter orders based on search term
  const filteredOrders = orders.filter(order => {
    const searchLower = searchTerm.toLowerCase();
    return (
      (order.id && order.id.toString().toLowerCase().includes(searchLower)) ||
      (order.username && order.username.toString().toLowerCase().includes(searchLower)) ||
      (order.email && order.email.toString().toLowerCase().includes(searchLower))
    );
  });

  // Pagination
  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

  // Format status for display
  const formatStatus = (status: string) => {
    if (!status) return 'Unknown';

    // Replace underscores with spaces and capitalize each word
    return status
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Status badge color
  const getStatusColor = (status: string) => {
    const statusLower = status?.toLowerCase() || '';

    if (statusLower.includes('complet')) {
      return 'bg-green-100 text-green-800';
    } else if (statusLower.includes('fail')) {
      return 'bg-red-100 text-red-800';
    } else if (statusLower.includes('stage_2') || statusLower.includes('stage 2')) {
      return 'bg-blue-100 text-blue-800';
    } else if (statusLower.includes('live')) {
      return 'bg-purple-100 text-purple-800';
    } else if (statusLower.includes('run')) {
      return 'bg-yellow-100 text-yellow-800';
    } else if (statusLower.includes('pass')) {
      return 'bg-teal-100 text-teal-800';
    } else if (statusLower.includes('incomplet') || statusLower.includes('pending')) {
      return 'bg-gray-100 text-gray-800';
    } else {
      return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle pass order
  const handlePassOrder = async (orderId: string | number) => {
    try {
      setIsLoading(true);
      setLoadingOrderId(orderId);

      // Extract numeric part of order ID (remove "FxE" prefix if present)
      const numericOrderId = String(orderId).replace(/^FxE/i, '');

      const updatedOrder = await adminService.passOrder(numericOrderId);

      // Update the order in the orders array
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? updatedOrder : order
        )
      );

      // Call the onOrderUpdated callback
      onOrderUpdated(updatedOrder);
    } catch (error) {
      alert('Failed to pass order. Please try again.');
    } finally {
      setIsLoading(false);
      setLoadingOrderId(null);
    }
  };

  // Handle fail order
  const handleFailOrder = async (orderId: string | number) => {
    try {
      setIsLoading(true);
      setLoadingOrderId(orderId);

      // Extract numeric part of order ID (remove "FxE" prefix if present)
      const numericOrderId = String(orderId).replace(/^FxE/i, '');

      const updatedOrder = await adminService.failOrder(numericOrderId);

      // Update the order in the orders array
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? updatedOrder : order
        )
      );

      // Call the onOrderUpdated callback
      onOrderUpdated(updatedOrder);
    } catch (error) {
      alert('Failed to fail order. Please try again.');
    } finally {
      setIsLoading(false);
      setLoadingOrderId(null);
    }
  };

  return (
    <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
      <div className="px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
        <h2 className="text-lg font-medium text-white">{title}</h2>
        <div className="relative">
          <input
            type="text"
            placeholder="Search orders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-teal-500"
          />
          <svg
            className="absolute right-3 top-2.5 h-4 w-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-teal-500/20">
          <thead className="bg-[#070F1B]">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Order ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Challenge Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Account Size
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Platform
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Date
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-teal-500/20">
            {currentOrders.length > 0 ? (
              currentOrders.map((order) => (
                <tr key={order.id} className="hover:bg-[#070F1B]/50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                    {order.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    <div>{order.username}</div>
                    <div className="text-xs text-gray-400">{order.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {order.challenge_type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {order.account_size}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {order.platform}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                      {formatStatus(order.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {order.created_at ? new Date(order.created_at).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-green-400 hover:text-green-300 mr-3"
                      onClick={() => handlePassOrder(order.id)}
                      disabled={isLoading && loadingOrderId === order.id}
                    >
                      {isLoading && loadingOrderId === order.id ? 'Processing...' : 'Pass'}
                    </button>
                    <button
                      className="text-red-400 hover:text-red-300"
                      onClick={() => handleFailOrder(order.id)}
                      disabled={isLoading && loadingOrderId === order.id}
                    >
                      {isLoading && loadingOrderId === order.id ? 'Processing...' : 'Fail'}
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-6 py-4 text-center text-sm text-gray-400">
                  No orders found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {filteredOrders.length > 0 && (
        <div className="px-6 py-3 flex items-center justify-between border-t border-teal-500/20">
          <div className="text-sm text-gray-400">
            Showing {indexOfFirstOrder + 1} to {Math.min(indexOfLastOrder, filteredOrders.length)} of {filteredOrders.length} orders
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded-md text-sm ${
                currentPage === 1
                  ? 'bg-[#070F1B]/50 text-gray-500 cursor-not-allowed'
                  : 'bg-[#070F1B] text-white hover:bg-teal-500/20'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded-md text-sm ${
                currentPage === totalPages
                  ? 'bg-[#070F1B]/50 text-gray-500 cursor-not-allowed'
                  : 'bg-[#070F1B] text-white hover:bg-teal-500/20'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* View Order Modal */}
      <ViewOrderModal
        order={selectedOrder}
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
      />
    </div>
  );
}
