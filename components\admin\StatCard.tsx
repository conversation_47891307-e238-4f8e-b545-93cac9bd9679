'use client';

interface StatCardProps {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
  change?: number;
  changeLabel?: string;
  className?: string;
}

export default function StatCard({ 
  title, 
  value, 
  icon, 
  change, 
  changeLabel,
  className = ''
}: StatCardProps) {
  return (
    <div className={`relative overflow-hidden rounded-xl bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 p-6 ${className}`}>
      <div className="flex justify-between">
        <div>
          <p className="text-sm text-gray-400">{title}</p>
          <p className="text-2xl font-semibold mt-2 text-white">{value}</p>
          
          {(change !== undefined || changeLabel) && (
            <div className="flex items-center mt-2">
              {change !== undefined && (
                <span className={`text-xs font-medium ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {change >= 0 ? '+' : ''}{change}%
                </span>
              )}
              {changeLabel && (
                <span className="text-xs text-gray-400 ml-1">
                  {changeLabel}
                </span>
              )}
            </div>
          )}
        </div>
        
        {icon && (
          <div className="text-teal-400">
            {icon}
          </div>
        )}
      </div>
      
      {/* Decorative gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-teal-500/0 via-teal-500/30 to-teal-500/0"></div>
    </div>
  );
}
