'use client';

import { useState } from 'react';
import { User } from '@/services/adminService';

interface UsersTableProps {
  users: User[];
}

export default function UsersTable({ users }: UsersTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const usersPerPage = 10;

  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase();
    return (
      (user.username && user.username.toString().toLowerCase().includes(searchLower)) ||
      (user.email && user.email.toString().toLowerCase().includes(searchLower)) ||
      (user.name && user.name.toString().toLowerCase().includes(searchLower)) ||
      (user.country && user.country.toString().toLowerCase().includes(searchLower)) ||
      (user.phone_no && user.phone_no.toString().toLowerCase().includes(searchLower))
    );
  });

  // Pagination
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  // Helper function to check verification status
  const isVerified = (status: boolean | string | number): boolean => {
    if (typeof status === 'boolean') return status;
    if (typeof status === 'number') return status === 1;
    return String(status).toLowerCase() === 'true' || status === '1';
  };

  return (
    <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl overflow-hidden">
      <div className="px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
        <div className="flex items-center">
          <h2 className="text-lg font-medium text-white">Users</h2>
          <span className="ml-2 px-2 py-1 bg-teal-500/20 rounded-full text-xs text-teal-400">
            {users.length} total
          </span>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-4 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-teal-500"
          />
          <svg
            className="absolute right-3 top-2.5 h-4 w-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-teal-500/20">
          <thead className="bg-[#070F1B]">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Username
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Country
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Verified
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-teal-500/20">
            {currentUsers.length > 0 ? (
              currentUsers.map((user) => (
                <tr key={user.id} className="hover:bg-[#070F1B]/50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                    {user.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {user.username}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {user.name || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {user.country || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      isVerified(user.is_verified)
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {isVerified(user.is_verified) ? 'Verified' : 'Not Verified'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      className="text-teal-400 hover:text-teal-300 mr-3 px-2 py-1 rounded-md hover:bg-teal-500/10 transition-colors"
                      onClick={() => {
                        alert(`User Details:\nID: ${user.id}\nUsername: ${user.username}\nEmail: ${user.email}\nName: ${user.name || 'N/A'}\nCountry: ${user.country || 'N/A'}\nPhone: ${user.phone_no || 'N/A'}\nAddress: ${user.address || 'N/A'}\nVerified: ${isVerified(user.is_verified) ? 'Yes' : 'No'}`);
                      }}
                    >
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View
                      </span>
                    </button>
                    <button
                      className="text-teal-400 hover:text-teal-300 px-2 py-1 rounded-md hover:bg-teal-500/10 transition-colors"
                      onClick={() => {
                        alert('Edit functionality will be implemented in a future update');
                      }}
                    >
                      <span className="flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit
                      </span>
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-400">
                  No users found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {filteredUsers.length > 0 && (
        <div className="px-6 py-3 flex items-center justify-between border-t border-teal-500/20">
          <div className="text-sm text-gray-400">
            Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} users
            {filteredUsers.length !== users.length && (
              <span className="ml-2 text-teal-400">
                (filtered from {users.length} total)
              </span>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded-md text-sm ${
                currentPage === 1
                  ? 'bg-[#070F1B]/50 text-gray-500 cursor-not-allowed'
                  : 'bg-[#070F1B] text-white hover:bg-teal-500/20'
              }`}
            >
              Previous
            </button>
            {totalPages > 2 && (
              <div className="flex items-center px-3 text-sm text-gray-400">
                Page {currentPage} of {totalPages}
              </div>
            )}
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded-md text-sm ${
                currentPage === totalPages
                  ? 'bg-[#070F1B]/50 text-gray-500 cursor-not-allowed'
                  : 'bg-[#070F1B] text-white hover:bg-teal-500/20'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
