'use client';

import { Order } from '@/services/adminService';
import Image from 'next/image';

interface ViewOrderModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (order: Order) => void;
}

export default function ViewOrderModal({ order, isOpen, onClose, onEdit }: ViewOrderModalProps) {
  if (!isOpen || !order) return null;

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Format status for display
  const formatStatus = (status: string) => {
    if (!status) return 'Unknown';

    // Replace underscores with spaces and capitalize each word
    return status
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Status badge color
  const getStatusColor = (status: string) => {
    const statusLower = status?.toLowerCase() || '';

    if (statusLower.includes('complet') || statusLower.includes('success')) {
      return 'bg-green-100 text-green-800';
    } else if (statusLower.includes('fail')) {
      return 'bg-red-100 text-red-800';
    } else if (statusLower.includes('stage') || statusLower.includes('stage_2') || statusLower.includes('stage2')) {
      return 'bg-blue-100 text-blue-800';
    } else if (statusLower.includes('live')) {
      return 'bg-purple-100 text-purple-800';
    } else if (statusLower.includes('run')) {
      return 'bg-yellow-100 text-yellow-800';
    } else if (statusLower.includes('pass')) {
      return 'bg-teal-100 text-teal-800';
    } else if (statusLower.includes('active')) {
      return 'bg-blue-100 text-blue-800';
    } else if (statusLower.includes('inactive')) {
      return 'bg-gray-100 text-gray-800';
    } else if (statusLower.includes('incomplet') || statusLower.includes('pending')) {
      return 'bg-gray-100 text-gray-800';
    } else {
      return 'bg-gray-100 text-gray-800';
    }
  };

  // Check if order has trading account details
  const hasAccountDetails = order.platform_login || order.platform_password || order.server || order.profit_target;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-black opacity-75"></div>
        </div>

        {/* Modal content */}
        <div className="inline-block align-bottom bg-[#0F1A2E] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-[#070F1B] px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Order Details</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Body */}
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left column - Order details */}
              <div>
                <div className="bg-[#0C1424]/80 rounded-lg border border-teal-500/20 p-5 mb-6">
                  <h4 className="text-white text-lg font-medium mb-4 border-b border-teal-500/20 pb-2 flex items-center">
                    <svg className="w-5 h-5 text-teal-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Order Information
                  </h4>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Order ID:</span>
                      <span className="text-white font-medium bg-[#070F1B] px-3 py-1 rounded">{order.order_id || order.id}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Username:</span>
                      <span className="text-white">{order.username}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Email:</span>
                      <span className="text-white">{order.email}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Challenge Type:</span>
                      <span className="text-white">{order.challenge_type}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Account Size:</span>
                      <span className="text-white font-medium text-teal-400">{order.account_size}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Platform:</span>
                      <span className="text-white">{order.platform}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Payment Method:</span>
                      <span className="text-white">{order.payment_method}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Transaction ID:</span>
                      <span className="text-white font-mono text-sm">{order.txid}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Status:</span>
                      <span className={`px-3 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                        {formatStatus(order.status)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                      <span className="text-gray-400">Created At:</span>
                      <span className="text-white text-sm">{formatDate(order.created_at)}</span>
                    </div>

                    <div className="flex justify-between items-center py-1.5">
                      <span className="text-gray-400">Updated At:</span>
                      <span className="text-white text-sm">{formatDate(order.updated_at)}</span>
                    </div>
                  </div>
                </div>

                {/* Trading Account Details */}
                <div className="bg-[#0C1424]/80 rounded-lg border border-teal-500/20 p-5 mb-6">
                  <h4 className="text-white text-lg font-medium mb-4 border-b border-teal-500/20 pb-2 flex items-center">
                    <svg className="w-5 h-5 text-teal-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    Trading Account Details
                  </h4>

                  {hasAccountDetails ? (
                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                        <span className="text-gray-400">Platform Login:</span>
                        <span className="text-white font-mono bg-[#070F1B] px-3 py-1 rounded">{order.platform_login || 'Not provided'}</span>
                      </div>

                      <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                        <span className="text-gray-400">Platform Password:</span>
                        <span className="text-white font-mono bg-[#070F1B] px-3 py-1 rounded">{order.platform_password || 'Not provided'}</span>
                      </div>

                      <div className="flex justify-between items-center py-1.5 border-b border-gray-700/30">
                        <span className="text-gray-400">Server:</span>
                        <span className="text-white">{order.server || 'Not provided'}</span>
                      </div>

                      <div className="flex justify-between items-center py-1.5">
                        <span className="text-gray-400">Profit Target:</span>
                        <span className="text-teal-400 font-medium">{order.profit_target ? `$${order.profit_target}` : 'Not provided'}</span>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-[#070F1B] border border-teal-500/10 rounded-lg p-6 flex flex-col items-center justify-center">
                      <svg className="w-12 h-12 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <p className="mt-4 text-gray-400 text-center">Trading account details not provided</p>
                      <p className="mt-2 text-gray-500 text-sm text-center">
                        {order.status.toLowerCase().includes('incomplet') || order.status.toLowerCase().includes('pending') ?
                          'Order is incomplete. Details will be available once the order is processed.' :
                          'No trading account details have been assigned to this order.'}
                      </p>
                    </div>
                  )}
                </div>

                {/* Order Timeline */}
                {order.timeline && order.timeline.length > 0 && (
                  <div className="bg-[#0C1424]/80 rounded-lg border border-teal-500/20 p-5">
                    <h4 className="text-white text-lg font-medium mb-4 border-b border-teal-500/20 pb-2 flex items-center">
                      <svg className="w-5 h-5 text-teal-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Order Timeline
                    </h4>

                    <div className="space-y-4 relative">
                      <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-teal-500/20"></div>

                      {order.timeline.map((event, index) => (
                        <div key={index} className="flex items-start ml-2">
                          <div className="relative">
                            <div className="h-6 w-6 rounded-full bg-teal-500/20 flex items-center justify-center -ml-3 z-10">
                              <div className="h-2 w-2 rounded-full bg-teal-500"></div>
                            </div>
                          </div>
                          <div className="ml-4 bg-[#070F1B] rounded-lg p-3 flex-1">
                            <div className="flex justify-between items-start">
                              <span className="text-white font-medium">
                                {event.event_type
                                  .replace(/_/g, ' ')
                                  .split(' ')
                                  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                  .join(' ')}
                              </span>
                              <span className="text-gray-400 text-xs">{formatDate(event.event_date)}</span>
                            </div>
                            <p className="text-gray-300 text-sm mt-1">{event.notes}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Right column - Payment proof image */}
              <div>
                <div className="bg-[#0C1424]/80 rounded-lg border border-teal-500/20 p-5">
                  <h4 className="text-white text-lg font-medium mb-4 border-b border-teal-500/20 pb-2 flex items-center">
                    <svg className="w-5 h-5 text-teal-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Payment Proof
                  </h4>

                  {order.image && (order.image.image_url || order.image.url) ? (
                    <div className="bg-[#070F1B] border border-teal-500/20 rounded-lg p-2">
                      <div className="relative h-80 w-full">
                        <img
                          src={order.image.url || order.image.image_url}
                          alt="Payment Proof"
                          className="rounded object-contain w-full h-full"
                          onError={(e) => {
                            // If image fails to load, show a placeholder
                            e.currentTarget.src = 'https://via.placeholder.com/400x300?text=Image+Not+Available';
                          }}
                        />
                        <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                          Payment Proof
                        </div>
                      </div>
                      <div className="mt-2 text-sm text-gray-400 flex items-center">
                        <svg className="w-4 h-4 text-teal-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Uploaded on: {formatDate(order.image.created_at)}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-[#070F1B] border border-teal-500/20 rounded-lg p-6 flex flex-col items-center justify-center h-80">
                      <svg className="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="mt-4 text-gray-400">No payment proof image available</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-[#070F1B] px-6 py-4 border-t border-teal-500/20 flex justify-between">
            <div className="text-gray-400 text-sm">
              <span className="text-teal-500">Order ID:</span> {order.order_id || order.id}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  onClose();
                  if (onEdit && order) {
                    onEdit(order);
                  }
                }}
                className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
              >
                <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Order
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-teal-600 text-white text-sm font-medium rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
