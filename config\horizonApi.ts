// Horizon API Configuration
export const HORIZON_API_CONFIG = {
  // Base URLs
  BASE_URL: 'https://fundedhorizon-back-e4285707ccdf.herokuapp.com',
  FALLBACK_URL: 'https://fundedhorizon-back-e4285707ccdf.herokuapp.com',

  // Security settings - Enable in production or when explicitly set
  USE_PROXY: false, // Disable proxying to always use direct backend URL
  MASK_URLS: false,

  // Timeout settings
  REQUEST_TIMEOUT: 30000, // 30 seconds

  // Headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  // /pendinglol - Get pending orders
  // /rlol - Get all rejected orders
  // /flol - Get all failed orders
  // /plol - Get all passed orders
  // /stlol - Get stage2 accounts
  // /llol - Get live accounts
  // /live_accounts - Get all live accounts
  // /stage2_accounts - Get all stage2
  // // Endpoints
  ENDPOINTS: {
    // Auth endpoints
    LOGIN: 'auth/login',
    SIGNUP: 'auth/signup',
    VERIFY_EMAIL: 'auth/verify-email',
    RESEND_VERIFICATION: 'auth/resend-verification',
    USERS: 'auth/users',

    // Order endpoints
    ORDERS: 'ordors/pendinglol',
    PENDING_ORDERS: 'ordors/pendinglol',
    COMPLETED_ORDERS: 'ordors/clol',
    FAILED_ORDERS: 'ordors/flol',
    PASSED_ORDERS: 'ordors/plol',
    STAGE2_ORDERS: 'ordors/stlol',
    LIVE_ORDERS: 'ordors/llol',
    RUNNING_ORDERS: 'ordors/running_orders',
    CERTIFICATES: 'ordors/certificates',
    SEND_CERTIFICATE: 'ordors/send_certificate',
    PLACE_ORDER: 'ordors/order',
    ORDER_STATUS: 'ordors/order_status',
    ORDER_DETAILS: 'ordors/order_details',
    ORDER_IDS: 'ordors/order_ids',
    COMPLETE_ORDER: 'ordors/clol',
    EDIT_COMPLETE_ORDER: 'ordors/edit_complete_order',
    FAIL_ORDER: 'ordors/fail_order',
    PASS_ORDER: 'ordors/pass_order',
    EDIT_PASSED_ORDER: 'ordors/edit_passed_order',
    UPDATE_ORDER_STATUS: 'ordors/update_status',
    UPDATE_ORDER_DETAILS: 'ordors/update_details',

    // Account endpoints
    ACCOUNT_CREDENTIALS: 'account/credentials',
    PENDING_CONTAINERS: 'account/credentials/pending',
    ASSIGN_CONTAINER: 'account/credentials',
  },

  // Error messages
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    TIMEOUT_ERROR: 'Request timeout. Please try again.',
    UNAUTHORIZED: 'Session expired. Please login again.',
    FORBIDDEN: 'Access denied.',
    NOT_FOUND: 'Resource not found.',
    SERVER_ERROR: 'Server error. Please try again later.',
    UNKNOWN_ERROR: 'An unknown error occurred.',
  },
} as const;

// Helper function to get full endpoint URL
export const getHorizonEndpointUrl = (endpoint: keyof typeof HORIZON_API_CONFIG.ENDPOINTS): string => {
  return HORIZON_API_CONFIG.ENDPOINTS[endpoint];
};

// Helper function to get error message
export const getHorizonErrorMessage = (statusCode: number): string => {
  switch (statusCode) {
    case 401:
      return HORIZON_API_CONFIG.ERROR_MESSAGES.UNAUTHORIZED;
    case 403:
      return HORIZON_API_CONFIG.ERROR_MESSAGES.FORBIDDEN;
    case 404:
      return HORIZON_API_CONFIG.ERROR_MESSAGES.NOT_FOUND;
    case 500:
    case 502:
    case 503:
    case 504:
      return HORIZON_API_CONFIG.ERROR_MESSAGES.SERVER_ERROR;
    default:
      return HORIZON_API_CONFIG.ERROR_MESSAGES.UNKNOWN_ERROR;
  }
}; 