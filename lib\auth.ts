import apiClient from '../services/api';

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'admin';
  createdAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData extends LoginCredentials {
  name: string;
}

// Auth state management will be implemented later
// TODO: Implement JWT or Supabase auth integration

/**
 * Login user
 */
export const login = async (credentials: LoginCredentials): Promise<User> => {
  // TODO: Implement actual login logic
  const response = await apiClient.post('/auth/login', credentials);
  return response.data.user;
};

/**
 * Register a new user
 */
export const register = async (data: RegisterData): Promise<User> => {
  // TODO: Implement actual registration logic
  const response = await apiClient.post('/auth/register', data);
  return response.data.user;
};

/**
 * Logout user
 */
export const logout = async (): Promise<void> => {
  // TODO: Implement actual logout logic
  await apiClient.post('/auth/logout');
  // Clear local storage/cookies
};

/**
 * Get current user
 */
export const getCurrentUser = async (): Promise<User | null> => {
  // TODO: Implement actual getCurrentUser logic
  try {
    const response = await apiClient.get('/auth/me');
    return response.data.user;
  } catch (error) {
    return null;
  }
}; 