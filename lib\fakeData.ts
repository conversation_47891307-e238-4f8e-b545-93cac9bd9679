import { Order, User, OrderStatus, AdminDashboardSummary } from '@/services/adminService';
import { ensureValidOrderSummary } from './orderSummaryFix';

// Generate a random date within the last 3 months
const getRandomDate = () => {
  const now = new Date();
  const threeMonthsAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
  const randomTime = threeMonthsAgo.getTime() + Math.random() * (now.getTime() - threeMonthsAgo.getTime());
  return new Date(randomTime).toISOString();
};

// Generate a random order ID
const generateOrderId = (index: number) => {
  return `FXE${10000 + index}`;
};

// Generate fake orders
export const generateFakeOrders = (count: number, status?: OrderStatus): Order[] => {
  const statuses: OrderStatus[] = [
    'incomplete', 'completed', 'failed', 'stage_2', 'live', 'running', 'passed'
  ];

  const platforms = ['MT4', 'MT5', 'cTrader'];
  const accountSizes = ['$5,000', '$10,000', '$25,000', '$50,000', '$100,000', '$200,000'];
  const challengeTypes = ['Standard', 'Express', 'Evaluation'];
  const paymentMethods = ['Credit Card', 'Bitcoin', 'Bank Transfer', 'PayPal'];

  return Array.from({ length: count }, (_, i) => {
    const orderStatus = status || statuses[Math.floor(Math.random() * statuses.length)];
    const createdAt = getRandomDate();
    const updatedAt = new Date(new Date(createdAt).getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();

    const order: Order = {
      id: generateOrderId(i),
      username: `trader${i + 1}`,
      email: `trader${i + 1}@example.com`,
      challenge_type: challengeTypes[Math.floor(Math.random() * challengeTypes.length)],
      account_size: accountSizes[Math.floor(Math.random() * accountSizes.length)],
      platform: platforms[Math.floor(Math.random() * platforms.length)],
      payment_method: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      txid: `tx_${Math.random().toString(36).substring(2, 10)}`,
      status: orderStatus,
      created_at: createdAt,
      updated_at: updatedAt,
      image: {
        image_url: 'https://via.placeholder.com/150',
        created_at: createdAt
      }
    };

    // Add trading account details for completed orders
    if (orderStatus !== 'incomplete' && orderStatus !== 'failed') {
      order.server = `${order.platform.toLowerCase()}-server-${Math.floor(Math.random() * 10) + 1}`;
      order.platform_login = `${100000 + i}`;
      order.platform_password = `pass_${Math.random().toString(36).substring(2, 10)}`;
      order.profit_target = Math.floor(Math.random() * 5000) + 1000;
      order.session_id = `session_${Math.random().toString(36).substring(2, 10)}`;
      order.terminal_id = Math.floor(Math.random() * 10000) + 1;
    }

    return order;
  });
};

// Generate fake users
export const generateFakeUsers = (count: number): User[] => {
  const countries = ['United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Spain', 'Italy', 'Japan', 'China', 'India', 'Brazil'];

  return Array.from({ length: count }, (_, i) => {
    const isVerified = Math.random() > 0.2; // 80% of users are verified

    return {
      id: i + 1,
      username: `user${i + 1}`,
      email: `user${i + 1}@example.com`,
      name: `User ${i + 1}`,
      country: countries[Math.floor(Math.random() * countries.length)],
      phone_no: `+1${Math.floor(Math.random() * 900) + 100}${Math.floor(Math.random() * 900) + 100}${Math.floor(Math.random() * 9000) + 1000}`,
      address: `${Math.floor(Math.random() * 9000) + 1000} Main St, City, State, 12345`,
      hashed_password: 'hashed_password',
      is_verified: isVerified
    };
  });
};

// Generate fake certificates
export const generateFakeCertificates = (count: number) => {
  const accountSizes = ['$5,000', '$10,000', '$25,000', '$50,000', '$100,000', '$200,000'];
  const challengeTypes = ['Standard', 'Express', 'Evaluation'];

  return Array.from({ length: count }, (_, i) => {
    return {
      certificate_number: `CERT-${1000 + i}`,
      order_id: `FXE${10000 + i}`,
      username: `trader${i + 1}`,
      issue_date: getRandomDate(),
      account_size: accountSizes[Math.floor(Math.random() * accountSizes.length)],
      challenge_type: challengeTypes[Math.floor(Math.random() * challengeTypes.length)],
      profit_target: Math.floor(Math.random() * 5000) + 1000
    };
  });
};

// Generate fake dashboard summary
export const generateFakeDashboardSummary = (): AdminDashboardSummary => {
  const totalOrders = 250;
  const totalUsers = 120;

  const completedOrders = 85;
  const failedOrders = 25;
  const passedOrders = 40;
  const stage2Orders = 30;
  const liveOrders = 20;
  const runningOrders = 15;
  const certificatesCount = 10;
  const incompleteOrders = totalOrders - (completedOrders + failedOrders + passedOrders + stage2Orders + liveOrders + runningOrders);

  return {
    totalOrders,
    totalUsers,
    orderSummary: ensureValidOrderSummary({
      total: totalOrders,
      completed: completedOrders,
      failed: failedOrders,
      passed: passedOrders,
      stage_2: stage2Orders,
      live: liveOrders,
      running: runningOrders,
      incomplete: incompleteOrders,
      certificates: certificatesCount
    })
  };
};

// Pre-generate some fake data
export const fakeData = {
  allOrders: generateFakeOrders(250),
  completedOrders: generateFakeOrders(85, 'completed'),
  failedOrders: generateFakeOrders(25, 'failed'),
  passedOrders: generateFakeOrders(40, 'passed'),
  stage2Orders: generateFakeOrders(30, 'stage_2'),
  liveOrders: generateFakeOrders(20, 'live'),
  runningOrders: generateFakeOrders(15, 'running'),
  incompleteOrders: generateFakeOrders(35, 'incomplete'),
  certificates: generateFakeCertificates(10),
  users: generateFakeUsers(120),
  dashboardSummary: generateFakeDashboardSummary()
};
