import { OrderSummary } from '@/services/adminService';

// This function creates a valid OrderSummary object with all required properties
export function createValidOrderSummary(): OrderSummary {
  return {
    total: 0,
    completed: 0,
    failed: 0,
    passed: 0,
    stage_2: 0,
    live: 0,
    running: 0,
    incomplete: 0,
    certificates: 0
  };
}

// This function is used to ensure that an OrderSummary object has all required properties
export function ensureValidOrderSummary(summary: Partial<OrderSummary>): OrderSummary {
  const validSummary = createValidOrderSummary();
  
  return {
    ...validSummary,
    ...summary
  };
}

// Use this function to fix any existing OrderSummary objects that might be missing properties
export function fixOrderSummary(summary: any): OrderSummary {
  if (!summary) {
    return createValidOrderSummary();
  }
  
  return {
    total: summary.total || 0,
    completed: summary.completed || 0,
    failed: summary.failed || 0,
    passed: summary.passed || 0,
    stage_2: summary.stage_2 || 0,
    live: summary.live || 0,
    running: summary.running || 0,
    incomplete: summary.incomplete || 0,
    certificates: summary.certificates || 0
  };
}
