import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Only apply to API requests
  if (request.nextUrl.pathname.startsWith('/auth/') || 
      request.nextUrl.pathname.startsWith('/order/') ||
      request.nextUrl.pathname.startsWith('/account/')) {
    
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-url', request.url)

    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    })
  }
}

export const config = {
  matcher: [
    '/auth/:path*',
    '/order/:path*',
    '/account/:path*'
  ]
} 