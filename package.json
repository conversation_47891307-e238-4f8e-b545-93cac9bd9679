{"name": "new-firm-client", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@radix-ui/react-slot": "^1.2.2", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "countries-list": "^3.1.1", "framer-motion": "^12.9.2", "lucide-react": "^0.487.0", "next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.3.5"}, "devDependencies": {"@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.31", "tailwind-scrollbar": "^3.0.0", "typescript": "^5.3.2"}}