import { Order, User, OrderStatus, AdminDashboardSummary } from './adminService';
import { fakeData } from '@/lib/fakeData';

// Mock admin service functions
export const mockAdminService = {
  // Get all orders
  getAllOrders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.allOrders);
      }, 500); // Simulate network delay
    });
  },

  // Get completed orders
  getCompletedOrders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.completedOrders);
      }, 500);
    });
  },

  // Get failed orders
  getFailedOrders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.failedOrders);
      }, 500);
    });
  },

  // Get passed orders
  getPassedOrders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.passedOrders);
      }, 500);
    });
  },

  // Get stage 2 orders
  getStage2Orders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.stage2Orders);
      }, 500);
    });
  },

  // Get live orders
  getLiveOrders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.liveOrders);
      }, 500);
    });
  },

  // Get running orders
  getRunningOrders: async (): Promise<Order[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.runningOrders);
      }, 500);
    });
  },

  // Get all users
  getAllUsers: async (): Promise<User[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.users);
      }, 500);
    });
  },

  // Get dashboard summary
  getDashboardSummary: async (): Promise<AdminDashboardSummary> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fakeData.dashboardSummary);
      }, 500);
    });
  },

  // Complete an order
  completeOrder: async (orderId: string, orderDetails: {
    platform_login: string;
    platform_password: string;
    server: string;
    profit_target?: number;
    session_id?: string;
    terminal_id?: number;
  }): Promise<Order> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = fakeData.allOrders.find(o => o.id === orderId);
        if (order) {
          const updatedOrder: Order = {
            ...order,
            status: 'completed',
            platform_login: orderDetails.platform_login,
            platform_password: orderDetails.platform_password,
            server: orderDetails.server,
            profit_target: orderDetails.profit_target,
            session_id: orderDetails.session_id,
            terminal_id: orderDetails.terminal_id,
            updated_at: new Date().toISOString()
          };
          resolve(updatedOrder);
        } else {
          throw new Error('Order not found');
        }
      }, 500);
    });
  },

  // Edit a completed order
  editCompletedOrder: async (orderId: string, orderDetails: {
    platform_login: string;
    platform_password: string;
    server: string;
    profit_target?: number;
    session_id?: string;
    terminal_id?: number;
  }): Promise<Order> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = fakeData.allOrders.find(o => o.id === orderId);
        if (order) {
          const updatedOrder: Order = {
            ...order,
            platform_login: orderDetails.platform_login,
            platform_password: orderDetails.platform_password,
            server: orderDetails.server,
            profit_target: orderDetails.profit_target,
            session_id: orderDetails.session_id,
            terminal_id: orderDetails.terminal_id,
            updated_at: new Date().toISOString()
          };
          resolve(updatedOrder);
        } else {
          throw new Error('Order not found');
        }
      }, 500);
    });
  },

  // Fail an order
  failOrder: async (orderId: string): Promise<Order> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = fakeData.allOrders.find(o => o.id === orderId);
        if (order) {
          const updatedOrder: Order = {
            ...order,
            status: 'failed',
            updated_at: new Date().toISOString()
          };
          resolve(updatedOrder);
        } else {
          throw new Error('Order not found');
        }
      }, 500);
    });
  },

  // Pass an order
  passOrder: async (orderId: string): Promise<Order> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = fakeData.allOrders.find(o => o.id === orderId);
        if (order) {
          const updatedOrder: Order = {
            ...order,
            status: 'passed',
            updated_at: new Date().toISOString()
          };
          resolve(updatedOrder);
        } else {
          throw new Error('Order not found');
        }
      }, 500);
    });
  },

  // Update order status
  updateOrderStatus: async (orderId: string, status: OrderStatus): Promise<Order> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = fakeData.allOrders.find(o => o.id === orderId);
        if (order) {
          const updatedOrder: Order = {
            ...order,
            status,
            updated_at: new Date().toISOString()
          };
          resolve(updatedOrder);
        } else {
          throw new Error('Order not found');
        }
      }, 500);
    });
  },

  // Update order details
  updateOrderDetails: async (orderId: string, orderDetails: {
    platform_login?: string;
    platform_password?: string;
    server?: string;
    profit_target?: number;
    session_id?: string;
    terminal_id?: number;
  }): Promise<Order> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const order = fakeData.allOrders.find(o => o.id === orderId);
        if (order) {
          const updatedOrder: Order = {
            ...order,
            ...orderDetails,
            updated_at: new Date().toISOString()
          };
          resolve(updatedOrder);
        } else {
          throw new Error('Order not found');
        }
      }, 500);
    });
  }
};
