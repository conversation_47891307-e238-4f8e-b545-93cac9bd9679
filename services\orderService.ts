import axios, { AxiosError } from 'axios';
import { authService, directApiCall } from './api';

// Types
export interface OrderResponse {
  order_id: string;
  status: string;
  message: string;
}

interface ErrorResponse {
  detail?: string;
  message?: string;
}

// Place order function
export const placeOrder = async (formData: FormData): Promise<OrderResponse> => {
  try {
    // Get token from localStorage if it exists
    const token = authService.getToken();



    // Set headers
    const headers: Record<string, string> = {
      'Accept': 'application/json',
    };

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    } else {
      throw new Error('You must be logged in to place an order');
    }

    // Make the request using directApiCall for URL masking
    const response = await directApiCall('order/order', {
      method: 'POST',
      data: formData,
      headers
    });

    return response;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<ErrorResponse>;
      if (axiosError.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        throw new Error(axiosError.response.data?.detail || axiosError.response.data?.message || 'Failed to place order');
      } else if (axiosError.request) {
        // The request was made but no response was received
        throw new Error('No response received from server');
      } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error('Error setting up the request');
      }
    }
    throw error;
  }
};

// Get order status
export const getOrderStatus = async (orderId: string): Promise<any> => {
  try {
    // Get token from localStorage if it exists
    const token = authService.getToken();

    // Set headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Remove FxE prefix if present
    const numericOrderId = orderId.replace(/^FxE/, '');

    // Make the request using directApiCall for URL masking
    const response = await directApiCall(`order/order_status/${numericOrderId}`, {
      method: 'GET',
      headers
    });

    return response;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<ErrorResponse>;
      if (axiosError.response) {
        throw new Error(axiosError.response.data?.detail || axiosError.response.data?.message || `Failed to get status for order ${orderId}`);
      } else if (axiosError.request) {
        throw new Error('No response received from server');
      } else {
        throw new Error('Error setting up the request');
      }
    }
    throw error;
  }
};

export default {
  placeOrder,
  getOrderStatus,
};
