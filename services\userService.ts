import { directApiCall } from './api';

// Types
export interface OrderDetails {
  id: string;
  challenge_type: string;
  account_size: string;
  platform: string;
  username: string;
  server: string;
  platform_login: string;
  platform_password: string;
  session_id: string;
  terminal_id: number;
  profit_target: number;
  status: string;
}

// User service functions
export const userService = {
  // Get user's order IDs
  getUserOrderIds: async (): Promise<string[]> => {
    try {
      const data = await directApiCall('order/order_ids');
      // If data is an array of objects with order_id property, extract just the order_id values
      if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object' && 'order_id' in data[0]) {
        return data.map(item => item.order_id);
      }
      // If data is already an array of strings, return it as is
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get order details by ID
  getOrderDetails: async (orderId: string): Promise<OrderDetails | null> => {
    try {
      // Remove FxE prefix if present
      const numericOrderId = orderId.replace(/^FxE/, '');
      const data = await directApiCall(`order/order_details/${numericOrderId}`);
      return data;
    } catch (error) {
      return null;
    }
  },

  // Save selected order ID to local storage
  saveSelectedOrderId: (orderId: string): void => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedOrderId', orderId);
    }
  },

  // Get selected order ID from local storage
  getSelectedOrderId: (): string | null => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('selectedOrderId');
    }
    return null;
  }
};

export default userService;
