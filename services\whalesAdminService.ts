import axios from 'axios';
import { WHALES_API_CONFIG } from '@/config/whalesApi';

// Types (same as original adminService)
export interface TimelineEvent {
  event_type: string;
  event_date: string;
  notes: string;
}

export interface StageAccount {
  server: string;
  platform_login: string;
  platform_password: string;
  session_id: string;
  terminal_id: number;
  profit_target?: number;
  profit_share?: number;
  status: string;
  created_at: string;
}

export interface Order {
  id: string | number;
  order_id?: string;
  username: string;
  email: string;
  challenge_type: string;
  account_size: string;
  platform: string;
  payment_method?: string;
  txid?: string;
  status: OrderStatus | string;
  created_at: string | null;
  updated_at?: string | null;
  completed_at?: string | null;
  image?: {
    url?: string;
    image_url?: string;
    created_at: string;
  };
  timeline?: TimelineEvent[];
  // Additional fields for trading account details
  server?: string;
  platform_login?: string;
  platform_password?: string;
  profit_target?: number;
  session_id?: string;
  terminal_id?: number;
  drawdown?: number;
  container_id?: number;
  // Status flags
  completed?: boolean;
  passed?: boolean;
  failed?: boolean;
  is_active?: boolean;
  is_stage2?: boolean;
  stage2_account?: StageAccount;
  is_live?: boolean;
  live_account?: StageAccount;
  complete_order_id?: number;
  // Failed order specific fields
  fail_order_id?: number;
  reason?: FailureReason;
  // Passed order specific fields
  pass_order_id?: number;
  pass_date?: string;
  profit_amount?: number;
  notes?: string;
  // Stage 2 specific fields
  stage2_id?: number;
  // Live account specific fields
  live_account_id?: number;
  // Running order specific fields
  is_running?: boolean;
}

export interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  country: string;
  phone_no: string;
  address: string;
  hashed_password: string;
  is_verified: boolean | string | number;
}

export interface Certificate {
  certificate_number: string;
  order_id: string;
  username: string;
  issue_date: string;
  account_size: string;
  challenge_type: string;
  profit_target: number;
}

export type OrderStatus =
  | 'incomplete'
  | 'completed'
  | 'failed'
  | 'stage_2'
  | 'live'
  | 'running'
  | 'active'
  | 'inactive'
  | 'passed';

export enum FailureReason {
  DAILY_DRAWDOWN_2_5 = "2.5% daily drawdown limit hit",
  DAILY_DRAWDOWN_4 = "4% daily drawdown limit hit",
  OVERALL_DRAWDOWN = "Overall drawdown limit hit",
  MINIMUM_TRADING_DAYS = "Minimum Trading Days",
  OTHER = 'OTHER'
}

export interface OrderSummary {
  total: number;
  completed: number;
  failed: number;
  passed: number;
  stage_2: number;
  live: number;
  running: number;
  incomplete: number;
  certificates: number;
}

export interface AdminDashboardSummary {
  totalOrders: number;
  totalUsers: number;
  orderSummary: OrderSummary;
}

export interface ContainerAccount {
  id: number;
  platform: string;
  server: string;
  platform_login: string;
  platform_password: string;
  account_size: string;
  account_type: string;
  is_assigned: boolean;
  order_id: number | null;
  created_at: string;
  updated_at: string | null;
  status: string;
}

// Helper function for direct API calls to Whales backend
const whalesApiCall = async (url: string, options: any = {}) => {
  try {
    // Get token from localStorage if it exists
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

    // Check if data is FormData
    const isFormData = options.data instanceof FormData;

    // Set default headers
    const headers = {
      // Only set Content-Type if not FormData (axios will set it with boundary for FormData)
      ...(!isFormData && { 'Content-Type': 'application/json' }),
      'Accept': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...(options.headers || {})
    };

    // Clean the endpoint
    const cleanEndpoint = url.startsWith('/') ? url.slice(1) : url;
    const apiUrl = `${WHALES_API_CONFIG.BASE_URL}/${cleanEndpoint}`;

    const response = await axios({
      url: apiUrl,
      method: options.method || 'GET',
      data: options.data,
      params: options.params,
      headers,
      timeout: WHALES_API_CONFIG.REQUEST_TIMEOUT,
      withCredentials: false,
    });

    return response.data;
  } catch (error) {
    throw error;
  }
};

// Whales admin service functions
export const whalesAdminService = {
  // Get all orders
  getAllOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get pending orders
  getPendingOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/pending_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get completed orders
  getCompletedOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/completed_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get failed orders
  getFailedOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/failed_orders');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the failed orders data to the Order interface
      return data.map((failedOrder: any) => ({
        id: failedOrder.fail_order_id || failedOrder.order_id,
        order_id: failedOrder.order_id,
        username: failedOrder.username,
        email: failedOrder.email,
        challenge_type: failedOrder.challenge_type,
        account_size: failedOrder.account_size,
        platform: failedOrder.platform,
        payment_method: failedOrder.payment_method,
        txid: failedOrder.txid,
        status: 'failed',
        created_at: failedOrder.created_at || new Date().toISOString(),
        server: failedOrder.server,
        platform_login: failedOrder.platform_login,
        platform_password: failedOrder.platform_password,
        image: failedOrder.image,
        failed: true,
        reason: failedOrder.reason
      }));
    } catch (error) {
      return [];
    }
  },

  // Get passed orders
  getPassedOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/passed_orders');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the passed orders data to the Order interface
      return data.map((passedOrder: any) => ({
        id: passedOrder.pass_order_id || passedOrder.order_id,
        order_id: passedOrder.order_id,
        username: passedOrder.username,
        email: passedOrder.email,
        challenge_type: passedOrder.challenge_type,
        account_size: passedOrder.account_size,
        platform: passedOrder.platform,
        status: 'passed',
        created_at: passedOrder.pass_date || new Date().toISOString(),
        passed: true,
        profit_amount: passedOrder.profit_amount,
        notes: passedOrder.notes
      }));
    } catch (error) {
      return [];
    }
  },

  // Get stage 2 orders
  getStage2Orders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/stage2_accounts');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Get live orders
  getLiveOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/live_accounts');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Get running orders
  getRunningOrders: async (): Promise<Order[]> => {
    try {
      const data = await whalesApiCall('order/running_orders');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Get all users
  getAllUsers: async (): Promise<User[]> => {
    try {
      const data = await whalesApiCall('auth/users');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Get certificates
  getCertificates: async (): Promise<Certificate[]> => {
    try {
      const data = await whalesApiCall('order/certificates');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Get dashboard summary
  getDashboardSummary: async (): Promise<AdminDashboardSummary> => {
    try {
      const [orders, users, certificates] = await Promise.all([
        whalesAdminService.getAllOrders(),
        whalesAdminService.getAllUsers(),
        whalesAdminService.getCertificates()
      ]);

      const orderSummary: OrderSummary = {
        total: orders.length,
        completed: orders.filter(o => o.status === 'completed').length,
        failed: orders.filter(o => o.status === 'failed').length,
        passed: orders.filter(o => o.status === 'passed').length,
        stage_2: orders.filter(o => o.status === 'stage_2').length,
        live: orders.filter(o => o.status === 'live').length,
        running: orders.filter(o => o.status === 'running').length,
        incomplete: orders.filter(o => o.status === 'incomplete').length,
        certificates: certificates.length
      };

      return {
        totalOrders: orders.length,
        totalUsers: users.length,
        orderSummary
      };
    } catch (error) {
      return {
        totalOrders: 0,
        totalUsers: 0,
        orderSummary: {
          total: 0,
          completed: 0,
          failed: 0,
          passed: 0,
          stage_2: 0,
          live: 0,
          running: 0,
          incomplete: 0,
          certificates: 0
        }
      };
    }
  },

  // Get containers
  getContainers: async (): Promise<ContainerAccount[]> => {
    try {
      const data = await whalesApiCall('account/credentials');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Create container
  createContainer: async (containerData: {
    platform: string;
    server: string;
    platform_login: string;
    platform_password: string;
    account_size: string;
    account_type: string;
  }): Promise<ContainerAccount> => {
    try {
      const data = await whalesApiCall('account/credentials', {
        method: 'POST',
        data: containerData
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Get pending containers
  getPendingContainers: async (): Promise<ContainerAccount[]> => {
    try {
      const data = await whalesApiCall('account/credentials/pending');
      return data || [];
    } catch (error) {
      return [];
    }
  },

  // Assign container to order
  assignContainerToOrder: async (credentialId: number, orderId: string | number, profitTarget: number): Promise<ContainerAccount> => {
    try {
      const data = await whalesApiCall(`account/credentials/${credentialId}`, {
        method: 'PUT',
        data: {
          order_id: orderId,
          profit_target: profitTarget
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
}; 