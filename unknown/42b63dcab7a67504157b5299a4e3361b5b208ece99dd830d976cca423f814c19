'use client';

import { useState, useEffect } from 'react';
import { Order, OrderStatus, adminService, ContainerAccount, FailureReason } from '@/services/adminService';

interface EditOrderModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated: (updatedOrder: Order) => void;
}

export default function EditOrderModal({ order, isOpen, onClose, onOrderUpdated }: EditOrderModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEdited, setIsEdited] = useState(false);
  const [isConfirmMode, setIsConfirmMode] = useState(false);
  const [containers, setContainers] = useState<ContainerAccount[]>([]);
  const [selectedContainer, setSelectedContainer] = useState<ContainerAccount | null>(null);
  const [formData, setFormData] = useState<{
    status: OrderStatus | string;
    platform_login: string;
    platform_password: string;
    server: string;
    profit_target: string;
    session_id: string;
    terminal_id: string;
    drawdown: string;
    container_id: string;
    // Additional fields for passed orders
    profit_amount: string;
    notes: string;
    account_type: 'stage2' | 'live' | '';
    // Additional fields for failed orders
    fail_reason: FailureReason | '';
    fail_date: string;
  }>({
    status: 'incomplete',
    platform_login: '',
    platform_password: '',
    server: '',
    profit_target: '',
    session_id: '',
    terminal_id: '',
    drawdown: '',
    container_id: '',
    // Additional fields for passed orders
    profit_amount: '',
    notes: '',
    account_type: '',
    // Additional fields for failed orders
    fail_reason: '',
    fail_date: new Date().toISOString().split('T')[0],
  });

  // Fetch containers when modal opens
  useEffect(() => {
    if (isOpen) {
      const fetchContainers = async () => {
        try {
          const data = await adminService.getPendingContainers();
          setContainers(data);
        } catch (error) {
          // Error handling without logging
        }
      };
      fetchContainers();
    }
  }, [isOpen]);

  // Reset form data when order changes
  useEffect(() => {
    if (order) {
      setFormData({
        status: order.status,
        platform_login: order.platform_login || '',
        platform_password: order.platform_password || '',
        server: order.server || '',
        profit_target: order.profit_target ? order.profit_target.toString() : '',
        session_id: order.session_id || '',
        terminal_id: order.terminal_id ? order.terminal_id.toString() : '',
        drawdown: order.drawdown ? order.drawdown.toString() : '',
        container_id: order.container_id ? order.container_id.toString() : '',
        // Additional fields for passed orders
        profit_amount: order.profit_amount ? order.profit_amount.toString() : '',
        notes: order.notes || '',
        account_type: '',  // This will be set when user selects stage2 or live
        // Additional fields for failed orders
        fail_reason: '',
        fail_date: new Date().toISOString().split('T')[0],
      });
      setIsEdited(false);
      setIsConfirmMode(false);
    }
  }, [order]);

  // Update form when container is selected
  useEffect(() => {
    if (selectedContainer) {
      setFormData(prev => ({
        ...prev,
        platform_login: selectedContainer.platform_login,
        platform_password: selectedContainer.platform_password,
        server: selectedContainer.server,
        container_id: selectedContainer.id.toString(),
      }));
      setIsEdited(true);
    }
  }, [selectedContainer]);

  if (!isOpen || !order) return null;

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setIsEdited(true);
    setIsConfirmMode(false);
  };

  // Handle container selection
  const handleContainerSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const containerId = e.target.value;
    const container = containers.find(c => c.id.toString() === containerId);
    setSelectedContainer(container || null);
  };

  // Helper function to check if status is incomplete
  const isIncomplete = (status: OrderStatus | string): boolean => {
    return status === 'incomplete' || status === 'Pending';
  };

  // Handle form submission - now just saves to local state
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate drawdown if provided
    if (formData.drawdown && (parseFloat(formData.drawdown) < 0 || parseFloat(formData.drawdown) > 100)) {
      setError('Drawdown must be between 0 and 100 percent');
      return;
    }

    setIsConfirmMode(true);
  };

  // Handle confirmation - this will call the backend
  const handleConfirm = async () => {
    setIsLoading(true);
    setError('');

    try {
      let finalOrder: Order;

      // Extract order ID and remove FxE prefix if present
      const orderIdStr = order.order_id
        ? (typeof order.order_id === 'string' ? order.order_id.replace(/^FxE/i, '') : String(order.order_id))
        : (typeof order.id === 'string' ? order.id.replace(/^FxE/i, '') : String(order.id));

      // First check if this is a passed order being moved to stage2 or live
      if (order.status === 'passed' && formData.account_type) {
        // Edit a passed order to move to stage2 or live
        console.log('Moving passed order to:', formData.account_type);
        finalOrder = await adminService.editPassedOrder(orderIdStr, {
          platform_login: formData.platform_login,
          platform_password: formData.platform_password,
          server: formData.server,
          terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
          session_id: formData.session_id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0,
          account_type: formData.account_type
        });
      }
      // If completing order with a container selected
      else if (formData.status === 'completed' && formData.container_id) {
        // Assign container to order
        const assignedContainer = await adminService.assignContainerToOrder(
          parseInt(formData.container_id),
          orderIdStr,
          formData.profit_target ? parseFloat(formData.profit_target) : 0
        );

        // Update the order with the container details
        finalOrder = {
          ...order,
          status: 'completed',
          platform_login: assignedContainer.platform_login,
          platform_password: assignedContainer.platform_password,
          server: assignedContainer.server,
          container_id: assignedContainer.id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : undefined
        };
      } else if (order.status === 'completed') {
        // Edit a completed order
        console.log('Editing completed order');
        const orderDetails = {
          platform_login: formData.platform_login,
          platform_password: formData.platform_password,
          server: formData.server,
          terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
          session_id: formData.session_id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0,
          drawdown: formData.drawdown ? parseFloat(formData.drawdown) : 0
        };
        finalOrder = await adminService.editCompletedOrder(orderIdStr, orderDetails);
      } else if (formData.status === 'completed' && order.status !== 'completed') {
        // Complete an order without container
        console.log('Completing order without container');
        const orderDetails = {
          platform_login: formData.platform_login,
          platform_password: formData.platform_password,
          server: formData.server,
          terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
          session_id: formData.session_id,
          profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0,
          drawdown: formData.drawdown ? parseFloat(formData.drawdown) : 0
        };
        finalOrder = await adminService.completeOrder(orderIdStr, orderDetails);
      } else if (formData.status === 'failed') {
        // Fail an order with reason and date
        const failDetails = {
          ...(formData.fail_reason && { reason: formData.fail_reason }),
          date: formData.fail_date
        };
        console.log('Failing order with details:', {
          orderId: orderIdStr,
          failDetails
        });
        finalOrder = await adminService.failOrder(orderIdStr, failDetails);
      } else if (formData.status === 'passed') {
        // Pass an order with profit amount and notes
        const passedOrderDetails = {
          profit_amount: formData.profit_amount ? parseFloat(formData.profit_amount) : 0,
          notes: formData.notes
        };
        console.log('Passing order with details:', passedOrderDetails);
        finalOrder = await adminService.passOrder(orderIdStr, passedOrderDetails);
      } else {
        // For other status changes, use the legacy methods
        console.log('Using legacy update methods');
        const statusUpdatedOrder = await adminService.updateOrderStatus(orderIdStr, formData.status as OrderStatus);

        // Only update order details if the status is not incomplete
        if (!isIncomplete(formData.status)) {
          const orderDetails = {
            platform_login: formData.platform_login,
            platform_password: formData.platform_password,
            server: formData.server,
            terminal_id: formData.terminal_id ? parseInt(formData.terminal_id) : 0,
            session_id: formData.session_id,
            profit_target: formData.profit_target ? parseFloat(formData.profit_target) : 0,
            drawdown: formData.drawdown ? parseFloat(formData.drawdown) : 0
          };
          const updatedOrder = await adminService.updateOrderDetails(orderIdStr, orderDetails);

          // Combine the updates
          finalOrder = {
            ...statusUpdatedOrder,
            ...updatedOrder
          };
        } else {
          finalOrder = statusUpdatedOrder;
        }
      }

      // Call the onOrderUpdated callback with the updated order
      onOrderUpdated(finalOrder);

      // Reset states
      setIsEdited(false);
      setIsConfirmMode(false);

      // Close the modal
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to update order. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-black opacity-75"></div>
        </div>

        {/* Modal content */}
        <div className="inline-block align-bottom bg-[#0F1A2E] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-[#070F1B] px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Edit Order</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Body */}
          <form onSubmit={handleSubmit}>
            <div className="px-6 py-4">
              {error && (
                <div className="mb-4 bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg">
                  {error}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="mb-4">
                  <div className="text-gray-400 mb-2">Order ID</div>
                  <div className="text-white font-medium">
                    {order.order_id ? 
                      (typeof order.order_id === 'string' ? order.order_id.replace(/^FxE/i, '') : order.order_id) 
                      : (typeof order.id === 'string' ? order.id.replace(/^FxE/i, '') : order.id)}
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-gray-400 mb-2">User</div>
                  <div className="text-white">{order.username} ({order.email})</div>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-white text-lg font-medium mb-3 border-b border-teal-500/20 pb-2">Order Status</h3>
                <div className="mb-4">
                  <label htmlFor="status" className="block text-gray-400 mb-2">
                    Status
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                  >
                    <option value="incomplete">Incomplete</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                    <option value="stage_2">Stage 2</option>
                    <option value="live">Live</option>
                    <option value="running">Running</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="passed">Passed</option>
                  </select>
                </div>

                {/* Additional fields for passed orders */}
                {(formData.status === 'passed' || order.status === 'passed') && (
                  <div className="mt-4 p-4 bg-[#070F1B]/50 border border-teal-500/10 rounded-lg">
                    <h4 className="text-white font-medium mb-3">Passed Order Details</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label htmlFor="profit_amount" className="block text-gray-400 mb-2">
                          Profit Amount
                        </label>
                        <div className="relative">
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">$</span>
                          <input
                            type="number"
                            id="profit_amount"
                            name="profit_amount"
                            value={formData.profit_amount}
                            onChange={handleChange}
                            className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 pl-8 pr-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                            placeholder="Enter profit amount"
                          />
                        </div>
                      </div>

                      <div>
                        <label htmlFor="notes" className="block text-gray-400 mb-2">
                          Notes
                        </label>
                        <input
                          type="text"
                          id="notes"
                          name="notes"
                          value={formData.notes}
                          onChange={handleChange}
                          className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                          placeholder="Enter notes"
                        />
                      </div>
                    </div>

                    {/* Account type selection for passed orders */}
                    {order.status === 'passed' && (
                      <div>
                        <label htmlFor="account_type" className="block text-gray-400 mb-2">
                          Move to Account Type
                        </label>
                        <select
                          id="account_type"
                          name="account_type"
                          value={formData.account_type}
                          onChange={handleChange}
                          className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        >
                          <option value="">Select Account Type</option>
                          <option value="stage2">Stage 2</option>
                          <option value="live">Live</option>
                        </select>
                        <p className="mt-2 text-xs text-gray-500">
                          Select an account type to move this passed order to Stage 2 or Live status.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Additional fields for failed orders */}
                {formData.status === 'failed' && (
                  <div className="mt-4 p-4 bg-[#070F1B]/50 border border-teal-500/10 rounded-lg">
                    <h4 className="text-white font-medium mb-3">Failed Order Details</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label htmlFor="fail_reason" className="block text-gray-400 mb-2">
                          Reason for Failure
                        </label>
                        <select
                          id="fail_reason"
                          name="fail_reason"
                          value={formData.fail_reason}
                          onChange={handleChange}
                          className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        >
                          <option value="">Select a reason</option>
                          {Object.values(FailureReason).map((reason) => (
                            <option key={reason} value={reason}>
                              {reason.replace(/_/g, ' ')}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label htmlFor="fail_date" className="block text-gray-400 mb-2">
                          Failure Date
                        </label>
                        <input
                          type="date"
                          id="fail_date"
                          name="fail_date"
                          value={formData.fail_date}
                          onChange={handleChange}
                          className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mb-4">
                <h3 className="text-white text-lg font-medium mb-3 border-b border-teal-500/20 pb-2">Trading Account Details</h3>

                {/* Container Account Selection */}
                <div className="mb-4">
                  <label htmlFor="container_id" className="block text-gray-400 mb-2">
                    Select Container Account
                  </label>
                  <select
                    id="container_id"
                    name="container_id"
                    value={formData.container_id}
                    onChange={handleContainerSelect}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                  >
                    <option value="">Select a container</option>
                    {containers.map((container) => (
                      <option key={container.id} value={container.id}>
                        {container.platform.toUpperCase()} - {container.account_type} (${container.account_size})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Show a message if the order is incomplete */}
                {formData.status === 'incomplete' ? (
                  <div className="bg-[#070F1B] border border-teal-500/10 rounded-lg p-6 flex flex-col items-center justify-center mb-4">
                    <svg className="w-12 h-12 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <p className="mt-4 text-gray-400 text-center">Trading account details cannot be edited</p>
                    <p className="mt-2 text-gray-500 text-sm text-center">
                      Order is incomplete. Change the status to completed or another active status to edit trading account details.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="mb-4">
                      <label htmlFor="platform_login" className="block text-gray-400 mb-2">
                        Platform Login
                      </label>
                      <input
                        type="text"
                        id="platform_login"
                        name="platform_login"
                        value={formData.platform_login || 'Not provided'}
                        onChange={handleChange}
                        disabled={isIncomplete(formData.status)}
                        className={`w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500 ${isIncomplete(formData.status) ? 'opacity-70 cursor-not-allowed' : ''}`}
                        placeholder="Enter platform login"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="platform_password" className="block text-gray-400 mb-2">
                        Platform Password
                      </label>
                      <input
                        type="text"
                        id="platform_password"
                        name="platform_password"
                        value={formData.platform_password}
                        onChange={handleChange}
                        className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        placeholder="Enter platform password"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="server" className="block text-gray-400 mb-2">
                        Server
                      </label>
                      <input
                        type="text"
                        id="server"
                        name="server"
                        value={formData.server}
                        onChange={handleChange}
                        className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        placeholder="Enter server name"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="profit_target" className="block text-gray-400 mb-2">
                        Profit Target
                      </label>
                      <div className="relative">
                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">$</span>
                        <input
                          type="number"
                          id="profit_target"
                          name="profit_target"
                          value={formData.profit_target}
                          onChange={handleChange}
                          className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 pl-8 pr-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                          placeholder="Enter profit target"
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="session_id" className="block text-gray-400 mb-2">
                        Session ID
                      </label>
                      <input
                        type="text"
                        id="session_id"
                        name="session_id"
                        value={formData.session_id}
                        onChange={handleChange}
                        className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        placeholder="Enter session ID"
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="terminal_id" className="block text-gray-400 mb-2">
                        Terminal ID
                      </label>
                      <input
                        type="number"
                        id="terminal_id"
                        name="terminal_id"
                        value={formData.terminal_id}
                        onChange={handleChange}
                        className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 px-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                        placeholder="Enter terminal ID"
                      />
                    </div>

                    {/* Drawdown Field */}
                    <div className="mb-4">
                      <label htmlFor="drawdown" className="block text-gray-400 mb-2">
                        Drawdown
                      </label>
                      <div className="relative">
                        <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">%</span>
                        <input
                          type="number"
                          id="drawdown"
                          name="drawdown"
                          value={formData.drawdown}
                          onChange={handleChange}
                          className="w-full bg-[#070F1B] border border-teal-500/20 rounded-lg py-2 pl-8 pr-3 text-white focus:outline-none focus:ring-1 focus:ring-teal-500"
                          placeholder="Enter drawdown percentage"
                          min="0"
                          max="100"
                          step="0.01"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="bg-[#070F1B] px-6 py-4 border-t border-teal-500/20 flex justify-end">
              <button
                type="button"
                onClick={onClose}
                className="mr-3 px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>

              {isConfirmMode ? (
                <button
                  type="button"
                  onClick={handleConfirm}
                  disabled={isLoading}
                  className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Confirming...
                    </div>
                  ) : (
                    'Confirm Changes'
                  )}
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 py-2 bg-teal-600 text-white text-sm font-medium rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </div>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
