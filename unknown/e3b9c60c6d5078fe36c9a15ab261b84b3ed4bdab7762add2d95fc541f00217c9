'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import EidSalePopup from '@/components/EidSalePopup';

export default function ReferralPage() {
  const [showEidPopup, setShowEidPopup] = useState(false);

  // Show Eid popup after page loads
  useEffect(() => {
    const timer = setTimeout(() => {
      // Check if user has dismissed the popup permanently
      const isDismissed = localStorage.getItem('eid-popup-dismissed');
      if (isDismissed === 'true') {
        return;
      }

      // Check if user has already seen the popup today
      const lastShown = localStorage.getItem('eid-popup-shown');
      const today = new Date().toDateString();

      if (lastShown !== today) {
        setShowEidPopup(true);
      }
    }, 2000); // Show after 2 seconds

    return () => clearTimeout(timer);
  }, []);

  const handleCloseEidPopup = () => {
    setShowEidPopup(false);
    // Remember that user has seen the popup today
    localStorage.setItem('eid-popup-shown', new Date().toDateString());
  };

  return (
    <div className="min-h-screen bg-[#030609] relative overflow-hidden">
      {/* Eid Sale Popup */}
      <EidSalePopup
        isOpen={showEidPopup}
        onClose={handleCloseEidPopup}
      />

      {/* Background Effects */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-[#030609]/90 via-[#050A10]/80 to-[#030609]/90"></div>
        <div className="absolute top-20 -left-32 w-96 h-96 rounded-full bg-gradient-to-r from-teal-500/5 via-teal-400/5 to-transparent blur-3xl transform -rotate-12 animate-pulse"></div>
        <div className="absolute bottom-20 -right-32 w-96 h-96 rounded-full bg-gradient-to-l from-blue-500/5 via-teal-400/5 to-transparent blur-3xl transform rotate-12 animate-pulse"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        {/* Coming Soon Banner */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="inline-block mb-6"
          >
            <div className="bg-teal-500/10 backdrop-blur-sm rounded-full px-6 py-2 border border-teal-500/20">
              <span className="text-teal-300 text-lg font-medium tracking-wide">COMING SOON</span>
            </div>
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Referral Program
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Join our community and earn rewards by referring traders to FXentra
          </motion.p>
        </div>

        {/* Program Preview */}
        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* How It Works */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-[#050A10]/50 backdrop-blur-xl rounded-2xl p-8 border border-white/5"
          >
            <h2 className="text-2xl font-bold text-white mb-6">How It Works</h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-500/20 flex items-center justify-center">
                  <span className="text-teal-400 font-bold">1</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Get Your Referral Link</h3>
                  <p className="text-gray-400">Receive a unique referral link when the program launches</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-500/20 flex items-center justify-center">
                  <span className="text-teal-400 font-bold">2</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Share With Traders</h3>
                  <p className="text-gray-400">Share your link with your trading community</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-500/20 flex items-center justify-center">
                  <span className="text-teal-400 font-bold">3</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Earn Rewards</h3>
                  <p className="text-gray-400">Get rewarded for every successful referral</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Benefits */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-[#050A10]/50 backdrop-blur-xl rounded-2xl p-8 border border-white/5"
          >
            <h2 className="text-2xl font-bold text-white mb-6">Program Benefits</h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-500/20 flex items-center justify-center">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Competitive Rewards</h3>
                  <p className="text-gray-400">Earn attractive commissions for each successful referral</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-500/20 flex items-center justify-center">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Secure & Reliable</h3>
                  <p className="text-gray-400">Track your referrals and earnings in real-time</p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-500/20 flex items-center justify-center">
                  <svg className="w-4 h-4 text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Community Growth</h3>
                  <p className="text-gray-400">Help build a stronger trading community</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="text-center mt-16"
        >
          <div className="bg-[#050A10]/50 backdrop-blur-xl rounded-2xl p-8 border border-white/5 max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4">Be the First to Know</h2>
            <p className="text-gray-300 mb-8">
              Join our waitlist to be notified when the referral program launches
            </p>
            <Link
              href="/signup"
              className="inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-xl text-gray-900 bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 shadow-lg shadow-teal-400/20 hover:shadow-teal-400/30 transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
            >
              Join Waitlist
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 