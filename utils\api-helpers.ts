import { AxiosError, AxiosResponse } from 'axios';

/**
 * Format error messages from API responses
 */
export const formatApiError = (error: AxiosError): string => {
  if (error.response?.data && typeof error.response.data === 'object') {
    const data = error.response.data as any;
    if (data.message) return data.message;
    if (data.error) return data.error;
  }
  return error.message || 'An unknown error occurred';
};

/**
 * Extract data from API responses
 */
export const extractApiData = <T>(response: AxiosResponse): T => {
  return response.data;
};

/**
 * Handle fetch errors for useEffect hooks
 */
export const handleFetchError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unknown error occurred';
};

/**
 * Format date strings for display
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};